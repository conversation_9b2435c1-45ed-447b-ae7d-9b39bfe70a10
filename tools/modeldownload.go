package tools

import (
	"archive/zip"
	"encoding/json"
	"fmt"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
)

const ModelRepositoryVersionUrl = "http://api.autopai.cloud.corpautohome.com/api/model/repository/version/latestValid"
const ModelRepositoryDownloadUrl = "http://api.autopai.cloud.corpautohome.com/api/model/repository/version/download?modelRepositoryName=%s&versionName=%s&appName=search_ad_ranker"

func DownloadAndUZipDeepModel(name string, dir string, modelVersion string) error {
	//defer util.Rec("模型" + name + "下载异常")
	filename := name + ".zip"
	filepath := dir + filename
	logger.Instance().Info("模型:%s 下载即将开始,filename:%s,filepath:%s", name, filename, filepath)
	_ = os.RemoveAll(filepath)

	downLoadUrl := fmt.Sprintf(ModelRepositoryDownloadUrl, url.QueryEscape(name), url.QueryEscape(modelVersion))
	logger.Instance().Info("模型:%s 下载开始,downLoadUrl:%s", name, downLoadUrl)
	err := DownloadFileRetry(downLoadUrl, dir, filename, 2)
	if err != nil {
		logger.Instance().Error("model %+v download fail ", name)
		return err
	}
	logger.Instance().Info("模型:%s 下载w结束", name)
	_ = os.RemoveAll(dir + name)
	err = Unzip(filepath, dir+name)
	if err != nil {
		logger.Instance().Error("model %+v unzip fail ", name)
		return err
	}
	logger.Instance().Warn("下载模型成功 zip:%s  解压到:%s", filepath, dir+name)
	return nil
}

func GetModelLastVersionByName(name string) (v string, t string, err error) {
	url := fmt.Sprintf("%s?modelRepositoryName=%s", ModelRepositoryVersionUrl, name)
	//var err error
	for retry := 3; retry > 0; retry-- {
		v, t, err = GetModelVersion(url)
		if err == nil && len(v) > 0 {
			return v, t, nil
		}
	}
	return "", "", err
	//panic(fmt.Errorf("调用模型仓库接口获取模型版本号失败 name:%v err:%v", name, err))
}

func GetModelVersion(durl string) (modelVersion string, pushTime string, err error) {
	client := &http.Client{}
	reqest, err := http.NewRequest("GET", durl, nil)
	resp, err := client.Do(reqest)
	if err != nil {
		return modelVersion, pushTime, err
	}
	defer resp.Body.Close()

	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Instance().Error("获取大盘ctr失败2,err:%v", err)
		return modelVersion, pushTime, err
	}
	response := &ModelVersionResp{}
	err = json.Unmarshal([]byte(respBytes), &response)
	if err == nil && response != nil && response.Data != nil {
		return response.Data.Version, response.Data.CreatedStimeStr, nil
	}
	return modelVersion, pushTime, err
}

type ModelVersionResp struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    *ModelVersionInfo `json:"data"`
}

type ModelVersionInfo struct {
	Version         string `json:"versionName"`
	CreatedStimeStr string `json:"createdStime"`
}

func Unzip(zipFile string, destDir string) error {
	zipReader, err := zip.OpenReader(zipFile)
	if err != nil {
		return err
	}
	defer zipReader.Close()

	for _, f := range zipReader.File {
		fpath := filepath.Join(destDir, f.Name)
		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, os.ModePerm)
		} else {
			if err = os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
				return err
			}

			inFile, err := f.Open()
			if err != nil {
				return err
			}
			defer inFile.Close()

			outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return err
			}
			defer outFile.Close()

			_, err = io.Copy(outFile, inFile)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func DownloadFileRetry(durl string, dir string, filename string, num int) (err error) {
	for retry := num; retry > 0; retry-- {
		err = DownloadFile(durl, dir, filename)
		if err == nil {
			return
		}
	}
	return
}

func DownloadFile(durl string, dir string, filename string) error {
	logger.Instance().Warn("filename:%s DownloadFile durl:%s", filename, durl)
	err := os.MkdirAll(dir, 0777)
	if err != nil {
		return err
	}

	//filename := path.Base(uri.Path)
	filepath := path.Join(dir, filename)

	_ = os.RemoveAll(filepath)
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	client := &http.Client{}
	reqest, err := http.NewRequest("GET", durl, nil)
	res, err := client.Do(reqest)
	//res, err := http.Get(durl)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	_, err = io.Copy(out, res.Body)

	return err
}
