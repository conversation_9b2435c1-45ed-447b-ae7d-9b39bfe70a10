package tools

import (
	"encoding/json"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

var (
	producer     sarama.AsyncProducer
	once         sync.Once
	kafkaTopic   string
	producerInit bool
)

// InitKafkaProducer 初始化Kafka生产者
func InitKafkaProducer(brokers []string, topic string) error {
	var err error
	once.Do(func() {
		config := sarama.NewConfig()
		config.Producer.Return.Successes = false
		config.Producer.Return.Errors = true
		config.Producer.RequiredAcks = sarama.WaitForLocal
		config.Producer.Compression = sarama.CompressionSnappy
		config.Producer.Flush.Frequency = 500 * time.Millisecond
		config.ChannelBufferSize = 10000

		// 创建Kafka 异步生产者
		producer, err = sarama.NewAsyncProducer(brokers, config)
		if err != nil {
			logger.Instance().Error("Failed to start Kafka producer: %v", err)
			return
		}

		kafkaTopic = topic
		producerInit = true

		// 处理错误
		go func() {
			for err := range producer.Errors() {
				logger.Instance().Error("Failed to write message to Kafka: %v", err)
			}
		}()
	})
	return err
}

// SendPredictionLog 发送预测日志到Kafka
func SendPredictionLog(log *models.PredictionLog) {
	if !producerInit {
		logger.Instance().Error("Kafka producer not initialized")
		return
	}

	jsonData, err := json.Marshal(log)
	if err != nil {
		logger.Instance().Error("Failed to marshal prediction log: %v", err)
		return
	}

	msg := &sarama.ProducerMessage{
		Topic: kafkaTopic,
		Value: sarama.StringEncoder(jsonData),
	}

	producer.Input() <- msg
}

// Close 关闭Kafka生产者
func CloseKafkaProducer() {
	if producer != nil {
		if err := producer.Close(); err != nil {
			logger.Instance().Error("Failed to close Kafka producer: %v", err)
		}
	}
}
