package process

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	d "gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"strconv"
	"time"
)

const (
	NameCtcvrOppo    = "ctcvroppo"
	NameCtcvrExpOppo = "ctcvroppoexp"
	NameCtcvrOppoCpi = "ctcvroppocpi"
	NameCPIExpOppo   = "NameCPIExp"
)

func init() {
	registry.RegisterFeaturesProcessing(NameCtcvrOppo, &CtcvrOppoProcess{})
	registry.RegisterFeaturesProcessing(NameCtcvrExpOppo, &CtcvrOppoExpProcess{})
	registry.RegisterFeaturesProcessing(NameCtcvrOppoCpi, &CtcvrOppoCpiProcess{})
	registry.RegisterFeaturesProcessing(NameCPIExpOppo, &CPIExpProcess{})
}

type CPIExpProcess struct {
	CtcvrOppoProcess
}

func (p *CPIExpProcess) Init(config map[string]string) {
	p.expName = models.CPIExp
}

type CtcvrOppoCpiProcess struct {
	CtcvrOppoProcess
}

func (p *CtcvrOppoCpiProcess) Init(config map[string]string) {
	p.expName = models.AB20Exp8Copy
}

type CtcvrOppoExpProcess struct {
	CtcvrOppoProcess
}

func (p *CtcvrOppoExpProcess) Init(config map[string]string) {
	p.expName = models.CPAExp
}

type CtcvrOppoProcess struct {
	expName string
}

func (p *CtcvrOppoProcess) Init(config map[string]string) {
	p.expName = models.CPAGroup
}

func (p *CtcvrOppoProcess) MachiningFeature(rc *models.RankContext) {

	if rc.InputDataExp == nil {
		rc.InputDataExp = make(map[string]*models.InputMetrics)
	}
	if rc.InputDataExp[p.expName] == nil {
		rc.InputDataExp[p.expName] = &models.InputMetrics{}
	}
	rc.InputDataExp[p.expName].CtrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureCPAAddedFeaNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureCPAAddedFeaNum),
	}
	rc.InputDataExp[p.expName].CvrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureCPAAddedFeaNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureCPAAddedFeaNum),
	}

	//fea2DataOppo(rc, rc.CtrCpa.LightgbmBase, rc.InputDataExp[p.expName].CtrData, p.expName)
	fea2DataOppo(rc, rc.CvrCpa.LightgbmBase, rc.InputDataExp[p.expName].CvrData, p.expName)

	rc.InputDataExp[p.expName].BiddingData = &models.FloatMetrics2[float32]{
		Data: make([][]float32, len(rc.ExpAds[p.expName])),
	}

	p.fea2DataCPABidding(rc, rc.BiddingV1.LightgbmBase, rc.InputDataExp[p.expName].BiddingData)

	return
}

func fea2DataOppo(rc *models.RankContext, base *data.LightgbmBase, data *models.FloatMetrics[float32], expname string) {
	impressionTime := rc.NowTime
	loc := time.FixedZone("Custom", tools.TimeOffset(rc.Request.DeviceInfo.CountryCode))
	impressionTime = impressionTime.In(loc)
	dayOfWeek := impressionTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", impressionTime.Hour())

	var (
		cat     = make([]float32, 0, FeatureCPAAddedListNum)
		show    = make([]float32, 0, FeatureCPAAddedListNum)
		click   = make([]float32, 0, FeatureCPAAddedListNum)
		ctrval  = make([]float32, 0, FeatureCPAAddedListNum)
		applist = make([]float32, FeatureAppsNum)
	)
	var (
		feaChannelId      = toNumFeaListSmooth(base, "channel_id", rc.Request.DeviceInfo.ChannelId, true)
		feaCountry        = toNumFeaListSmooth(base, "country", rc.Request.DeviceInfo.CountryCode, true)
		feaDayOfWeek      = toNumFeaListSmooth(base, "day_of_week", dayOfWeek, true)
		feaEventHour      = toNumFeaListSmooth(base, "event_hour", eventHour, true)
		feaDeviceBrand    = toNumFeaListSmooth(base, "device_brand", rc.Request.DeviceInfo.Brand, true)
		feaDeviceType     = toNumFeaListSmooth(base, "device_type", strconv.Itoa(int(rc.Request.DeviceInfo.DeviceType)), true)
		feaOs             = toNumFeaListSmooth(base, "os", rc.Request.DeviceInfo.Os, true)
		feaOsVersion      = toNumFeaListSmooth(base, "os_version", rc.Request.DeviceInfo.OsVersion, true)
		feaNetworkType    = toNumFeaListSmooth(base, "network_type", strconv.Itoa(int(rc.Request.DeviceInfo.NetworkType)), true)
		feaAdvertiserType = toNumFeaListSmooth(base, "advertiser_type", strconv.Itoa(int(rc.Request.AdSlotInfo.AdvertiserType)), true)
		feaAppId          = toNumFeaListSmooth(base, "app_id", rc.Request.AdSlotInfo.SourcePackage, true)
		feaTagId          = toNumFeaListSmooth(base, "tag_id", rc.Request.AdSlotInfo.AdSlotId, true)
		feaDeviceIdType   = toNumFeaListSmooth(base, "device_id_type", rc.Request.DeviceInfo.DeviceIdType, true)
		//feaTimeZone            = p.toNumFeaList(base, "timezone", rc.Request.DeviceInfo.Timezone, true)
		feaTrafficMaterialSize = toNumFeaListSmooth(base, "traffic_material_size", rc.Request.AdSlotInfo.Size, true)
		feaDeviceModel         = toNumFeaListSmooth(base, "device_model", rc.Request.DeviceInfo.Model, true)
	)
	var install []int32
	if rc.UserFeature != nil && len(rc.UserFeature.InstallList) > 0 {
		install = tools.MergeAndDeduplicate(rc.UserFeature.InstallList, rc.Request.DeviceInfo.InstallList)
	} else {
		install = rc.Request.DeviceInfo.InstallList
	}

	for _, v := range install {
		s := strconv.Itoa(int(v))
		if n, ok := base.AppIndex[s]; ok {
			applist[n] = 1
		}
	}

	for _, mi := range rc.ExpAds[expname] {
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]

		// 新增的CPA特征
		//"industry_ids", 从app表获取
		industryIds := d.AcInstance().GetIndustryIds(mi.TargetPackage)
		//"iab_id", 从app表获取
		iabId := d.AcInstance().GetIabId(mi.TargetPackage)
		//"iab_sub_id", 从app表获取
		iabSubId := d.AcInstance().GetIabSubId(mi.TargetPackage)
		//"conversion_type"
		conversionType := int(mi.ConversionType)

		//"channel_id"，
		cat, show, click, ctrval = append2lists(feaChannelId, cat, show, click, ctrval)
		//"ad_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "ad_id", mi.AdId, true), cat, show, click, ctrval)
		//"creative_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "creative_id", mi.CreativeGroupId, true), cat, show, click, ctrval)
		//"country",
		cat, show, click, ctrval = append2lists(feaCountry, cat, show, click, ctrval)
		//"day_of_week",
		cat, show, click, ctrval = append2lists(feaDayOfWeek, cat, show, click, ctrval)
		//"event_hour",
		cat, show, click, ctrval = append2lists(feaEventHour, cat, show, click, ctrval)
		//"device_brand",
		cat, show, click, ctrval = append2lists(feaDeviceBrand, cat, show, click, ctrval)
		//"device_type",
		cat, show, click, ctrval = append2lists(feaDeviceType, cat, show, click, ctrval)
		//"os",
		cat, show, click, ctrval = append2lists(feaOs, cat, show, click, ctrval)
		//"os_version",
		cat, show, click, ctrval = append2lists(feaOsVersion, cat, show, click, ctrval)
		//"network_type",
		cat, show, click, ctrval = append2lists(feaNetworkType, cat, show, click, ctrval)
		//"advertiser_type",  全是2
		cat, show, click, ctrval = append2lists(feaAdvertiserType, cat, show, click, ctrval)
		//"app_id", 有tag
		cat, show, click, ctrval = append2lists(feaAppId, cat, show, click, ctrval)
		//"tag_id",
		cat, show, click, ctrval = append2lists(feaTagId, cat, show, click, ctrval)
		//"package_name",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "package_name", mi.TargetPackage, true), cat, show, click, ctrval)
		//"device_id_type", 全是GPID
		cat, show, click, ctrval = append2lists(feaDeviceIdType, cat, show, click, ctrval)
		//"timezone", +00:00
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "timezone", mi.Timezone, true), cat, show, click, ctrval)
		//"account_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "account_id", strconv.Itoa(int(mi.AccountId)), true), cat, show, click, ctrval)
		//"project_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "project_id", strconv.Itoa(int(mi.ProjectId)), true), cat, show, click, ctrval)
		//"material_id_type",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "material_id_type", strconv.Itoa(int(mi.MaterialIdType)), true), cat, show, click, ctrval)
		//"title_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "title_id", mi.TitleId, true), cat, show, click, ctrval)
		//"description_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "description_id", mi.DescriptionId, true), cat, show, click, ctrval)
		//"delivery_type",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "delivery_type", strconv.Itoa(int(rc.Request.AdSlotInfo.DeliveryType)), true), cat, show, click, ctrval)
		//"traffic_material_size",
		cat, show, click, ctrval = append2lists(feaTrafficMaterialSize, cat, show, click, ctrval)
		//"delivery_material_size",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "delivery_material_size", mi.MaterialSize, true), cat, show, click, ctrval)
		//"image_material_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "image_material_id", mi.ImageMaterialId, true), cat, show, click, ctrval)
		//"video_material_id",
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "video_material_id", mi.VideoMaterialId, true), cat, show, click, ctrval)
		//"device_model",无tag
		cat, show, click, ctrval = append2lists(feaDeviceModel, cat, show, click, ctrval)
		// conversion_type
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "conversion_type", strconv.Itoa(conversionType), true), cat, show, click, ctrval)
		// industry_ids
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "industry_ids", strconv.Itoa(industryIds), true), cat, show, click, ctrval)
		// iab_id
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "iab_id", iabId, true), cat, show, click, ctrval)
		// iab_sub_id
		cat, show, click, ctrval = append2lists(toNumFeaListSmooth(base, "iab_sub_id", iabSubId, true), cat, show, click, ctrval)

		// add to rc
		data.Data = append(data.Data, cat...)
		data.Data = append(data.Data, show...)
		data.Data = append(data.Data, click...)
		data.Data = append(data.Data, ctrval...)
		data.Data = append(data.Data, applist...)
	}
}

func (p *CtcvrOppoProcess) fea2DataCPABidding(rc *models.RankContext, base *data.LightgbmBase, data *models.FloatMetrics2[float32]) {

	impressionTime := rc.NowTime
	loc := time.FixedZone("Custom", tools.TimeOffset(rc.Request.DeviceInfo.CountryCode))
	impressionTime = impressionTime.In(loc)
	dayOfWeek := impressionTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", impressionTime.Hour())

	var (
		cat    = make([]float32, 0, BiddingFeatureListNum)
		show   = make([]float32, 0, BiddingFeatureListNum)
		click  = make([]float32, 0, BiddingFeatureListNum)
		ctrval = make([]float32, 0, BiddingFeatureListNum)
	)
	var (
		feaBidPrice    = []float32{0, 0, 0, 0}
		feaChannelId   = toNumFeaList(base, "channel_id", rc.Request.DeviceInfo.ChannelId, true)
		feaCountry     = toNumFeaList(base, "country", rc.Request.DeviceInfo.CountryCode, true)
		feaDayOfWeek   = toNumFeaList(base, "day_of_week", dayOfWeek, true)
		feaEventHour   = toNumFeaList(base, "event_hour", eventHour, true)
		feaDeviceBrand = toNumFeaList(base, "device_brand", rc.Request.DeviceInfo.Brand, true)
		feaDeviceType  = toNumFeaList(base, "device_type", strconv.Itoa(int(rc.Request.DeviceInfo.DeviceType)), true)
		feaOs          = toNumFeaList(base, "os", rc.Request.DeviceInfo.Os, true)
		feaOsVersion   = toNumFeaList(base, "os_version", rc.Request.DeviceInfo.OsVersion, true)
		feaNetworkType = toNumFeaList(base, "network_type", strconv.Itoa(int(rc.Request.DeviceInfo.NetworkType)), true)
		//feaAdvertiserType = p.toNumFeaList(base, "advertiser_type", strconv.Itoa(int(rc.Request.AdSlotInfo.AdvertiserType)), true)
		feaAppId = toNumFeaList(base, "app_id", rc.Request.AdSlotInfo.SourcePackage, true)
		feaTagId = toNumFeaList(base, "tag_id", rc.Request.AdSlotInfo.AdSlotId, true)
		//feaDeviceIdType   = p.toNumFeaList(base, "device_id_type", rc.Request.DeviceInfo.DeviceIdType, true)
		//feaTimeZone            = p.toNumFeaList(base, "timezone", rc.Request.DeviceInfo.Timezone, true)
		feaTrafficMaterialSize = toNumFeaList(base, "traffic_material_size", rc.Request.AdSlotInfo.Size, true)
		feaDeviceModel         = toNumFeaList(base, "device_model", rc.Request.DeviceInfo.Model, true)
	)

	for i, mi := range rc.ExpAds[p.expName] {
		data.Data[i] = make([]float32, 0, BiddingFeatureNum) // 增加新特征的空间
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]

		//"channel_id"，
		cat, show, click, ctrval = append2lists(feaChannelId, cat, show, click, ctrval)
		//"country",
		cat, show, click, ctrval = append2lists(feaCountry, cat, show, click, ctrval)
		//"device_brand",
		cat, show, click, ctrval = append2lists(feaDeviceBrand, cat, show, click, ctrval)
		//"device_type",
		cat, show, click, ctrval = append2lists(feaDeviceType, cat, show, click, ctrval)
		//"os",
		cat, show, click, ctrval = append2lists(feaOs, cat, show, click, ctrval)
		//"os_version",
		cat, show, click, ctrval = append2lists(feaOsVersion, cat, show, click, ctrval)
		//"network_type",
		cat, show, click, ctrval = append2lists(feaNetworkType, cat, show, click, ctrval)
		//"app_id", 无tag
		cat, show, click, ctrval = append2lists(feaAppId, cat, show, click, ctrval)
		//"ad_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "ad_id", mi.AdId, true), cat, show, click, ctrval)
		//"material_id_type",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "material_id_type", strconv.Itoa(int(mi.MaterialIdType)), true), cat, show, click, ctrval)
		//"title_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "title_id", mi.TitleId, true), cat, show, click, ctrval)
		//"description_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "description_id", mi.DescriptionId, true), cat, show, click, ctrval)
		//"bid_price",
		cat, show, click, ctrval = append2lists(feaBidPrice, cat, show, click, ctrval)
		//"delivery_type",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "delivery_type", strconv.Itoa(int(rc.Request.AdSlotInfo.DeliveryType)), true), cat, show, click, ctrval)
		//"ad_slot_type",就是：material_type
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "ad_slot_type", strconv.Itoa(int(mi.MaterialType)), true), cat, show, click, ctrval)
		//"tag_id",
		cat, show, click, ctrval = append2lists(feaTagId, cat, show, click, ctrval)
		//"day_of_week",
		cat, show, click, ctrval = append2lists(feaDayOfWeek, cat, show, click, ctrval)
		//"event_hour",
		cat, show, click, ctrval = append2lists(feaEventHour, cat, show, click, ctrval)
		//"timezone", +00:00
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "timezone", mi.Timezone, true), cat, show, click, ctrval)
		//"account_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "account_id", strconv.Itoa(int(mi.AccountId)), true), cat, show, click, ctrval)
		//"project_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "project_id", strconv.Itoa(int(mi.ProjectId)), true), cat, show, click, ctrval)
		//delivery_material_size，
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "delivery_material_size", mi.MaterialSize, true), cat, show, click, ctrval)
		//"image_material_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "image_material_id", mi.ImageMaterialId, true), cat, show, click, ctrval)
		//"video_material_id",
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "video_material_id", mi.VideoMaterialId, true), cat, show, click, ctrval)
		//"traffic_material_size",
		cat, show, click, ctrval = append2lists(feaTrafficMaterialSize, cat, show, click, ctrval)
		//"device_model"
		cat, show, click, ctrval = append2lists(feaDeviceModel, cat, show, click, ctrval)
		//"delivery_package", 就是TargetPackage
		cat, show, click, ctrval = append2lists(toNumFeaList(base, "delivery_package", mi.TargetPackage, true), cat, show, click, ctrval)

		// add to rc
		data.Data[i] = append(data.Data[i], cat...)
		data.Data[i] = append(data.Data[i], show...)
		data.Data[i] = append(data.Data[i], click...)
		data.Data[i] = append(data.Data[i], ctrval...)
	}
}
