package process

import (
	"fmt"
	"strconv"
	"time"

	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
)

const (
	CtrV4CvrV2 = "ctcvr3.5"
)

func init() {
	registry.RegisterFeaturesProcessing(CtrV4CvrV2, &MixCtrCvrProcess{})
}

type MixCtrCvrProcess struct {
	expName string
}

func (p *MixCtrCvrProcess) Init(config map[string]string) {
	p.expName = models.Ctr4Cvr2
}

func (p *MixCtrCvrProcess) MachiningFeature(rc *models.RankContext) {

	if rc.InputDataExp == nil {
		rc.InputDataExp = make(map[string]*models.InputMetrics)
	}
	if rc.InputDataExp[p.expName] == nil {
		rc.InputDataExp[p.expName] = &models.InputMetrics{}
	}
	rc.InputDataExp[p.expName].CtrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureWithUidFeaNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureWithUidFeaNum),
	}
	rc.InputDataExp[p.expName].CvrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureAddedFeaNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureAddedFeaNum),
	}

	fea2DataUidFea(rc, rc.CtrV1.LightgbmBase, rc.InputDataExp[p.expName].CtrData, p.expName)
	fea2DataWithNewFea(rc, rc.CvrV1.LightgbmBase, rc.InputDataExp[p.expName].CvrData, p.expName)

	rc.InputDataExp[p.expName].BiddingData = &models.FloatMetrics2[float32]{
		Data: make([][]float32, len(rc.ExpAds[p.expName])),
	}

	p.fea2DataBidding(rc, rc.BiddingV1.LightgbmBase, rc.InputDataExp[p.expName].BiddingData)

	return
}

func (p *MixCtrCvrProcess) fea2DataBidding(rc *models.RankContext, base *data.LightgbmBase, data *models.FloatMetrics2[float32]) {

	impressionTime := rc.NowTime
	loc := time.FixedZone("Custom", tools.TimeOffset(rc.Request.DeviceInfo.CountryCode))
	impressionTime = impressionTime.In(loc)
	dayOfWeek := impressionTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", impressionTime.Hour())

	// 名字没修改
	var (
		cat    = make([]float32, 0, BiddingFeatureListNum)
		show   = make([]float32, 0, BiddingFeatureListNum)
		click  = make([]float32, 0, BiddingFeatureListNum)
		ctrval = make([]float32, 0, BiddingFeatureListNum)
	)
	var (
		feaBidPrice    = []float32{0, 0, 0, 0}
		feaChannelId   = p.toNumFeaList(base, "channel_id", rc.Request.DeviceInfo.ChannelId, true)
		feaCountry     = p.toNumFeaList(base, "country", rc.Request.DeviceInfo.CountryCode, true)
		feaDayOfWeek   = p.toNumFeaList(base, "day_of_week", dayOfWeek, true)
		feaEventHour   = p.toNumFeaList(base, "event_hour", eventHour, true)
		feaDeviceBrand = p.toNumFeaList(base, "device_brand", rc.Request.DeviceInfo.Brand, true)
		feaDeviceType  = p.toNumFeaList(base, "device_type", strconv.Itoa(int(rc.Request.DeviceInfo.DeviceType)), true)
		feaOs          = p.toNumFeaList(base, "os", rc.Request.DeviceInfo.Os, true)
		feaOsVersion   = p.toNumFeaList(base, "os_version", rc.Request.DeviceInfo.OsVersion, true)
		feaNetworkType = p.toNumFeaList(base, "network_type", strconv.Itoa(int(rc.Request.DeviceInfo.NetworkType)), true)
		//feaAdvertiserType = p.toNumFeaList(base, "advertiser_type", strconv.Itoa(int(rc.Request.AdSlotInfo.AdvertiserType)), true)
		feaAppId = p.toNumFeaList(base, "app_id", rc.Request.AdSlotInfo.SourcePackage, true)
		feaTagId = p.toNumFeaList(base, "tag_id", rc.Request.AdSlotInfo.AdSlotId, true)
		//feaDeviceIdType   = p.toNumFeaList(base, "device_id_type", rc.Request.DeviceInfo.DeviceIdType, true)
		//feaTimeZone            = p.toNumFeaList(base, "timezone", rc.Request.DeviceInfo.Timezone, true)
		feaTrafficMaterialSize = p.toNumFeaList(base, "traffic_material_size", rc.Request.AdSlotInfo.Size, true)
		feaDeviceModel         = p.toNumFeaList(base, "device_model", rc.Request.DeviceInfo.Model, true)
	)

	for i, mi := range rc.ExpAds[p.expName] {
		data.Data[i] = make([]float32, 0, BiddingFeatureNum)
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]
		//"channel_id"，
		cat, show, click, ctrval = append2lists(feaChannelId, cat, show, click, ctrval)
		//"country",
		cat, show, click, ctrval = append2lists(feaCountry, cat, show, click, ctrval)
		//"device_brand",
		cat, show, click, ctrval = append2lists(feaDeviceBrand, cat, show, click, ctrval)
		//"device_type",
		cat, show, click, ctrval = append2lists(feaDeviceType, cat, show, click, ctrval)
		//"os",
		cat, show, click, ctrval = append2lists(feaOs, cat, show, click, ctrval)
		//"os_version",
		cat, show, click, ctrval = append2lists(feaOsVersion, cat, show, click, ctrval)
		//"network_type",
		cat, show, click, ctrval = append2lists(feaNetworkType, cat, show, click, ctrval)
		//"app_id", 无tag
		cat, show, click, ctrval = append2lists(feaAppId, cat, show, click, ctrval)
		//"ad_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "ad_id", mi.AdId, true), cat, show, click, ctrval)
		//"material_id_type",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "material_id_type", strconv.Itoa(int(mi.MaterialIdType)), true), cat, show, click, ctrval)
		//"title_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "title_id", mi.TitleId, true), cat, show, click, ctrval)
		//"description_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "description_id", mi.DescriptionId, true), cat, show, click, ctrval)
		//"bid_price",
		cat, show, click, ctrval = append2lists(feaBidPrice, cat, show, click, ctrval)
		//"delivery_type",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "delivery_type", strconv.Itoa(int(rc.Request.AdSlotInfo.DeliveryType)), true), cat, show, click, ctrval)
		//"ad_slot_type",就是：material_type
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "ad_slot_type", strconv.Itoa(int(mi.MaterialType)), true), cat, show, click, ctrval)
		//"tag_id",
		cat, show, click, ctrval = append2lists(feaTagId, cat, show, click, ctrval)
		//"day_of_week",
		cat, show, click, ctrval = append2lists(feaDayOfWeek, cat, show, click, ctrval)
		//"event_hour",
		cat, show, click, ctrval = append2lists(feaEventHour, cat, show, click, ctrval)
		//"timezone", +00:00
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "timezone", mi.Timezone, true), cat, show, click, ctrval)
		//"account_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "account_id", strconv.Itoa(int(mi.AccountId)), true), cat, show, click, ctrval)
		//"project_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "project_id", strconv.Itoa(int(mi.ProjectId)), true), cat, show, click, ctrval)
		//delivery_material_size，
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "delivery_material_size", mi.MaterialSize, true), cat, show, click, ctrval)
		//"image_material_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "image_material_id", mi.ImageMaterialId, true), cat, show, click, ctrval)
		//"video_material_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "video_material_id", mi.VideoMaterialId, true), cat, show, click, ctrval)
		//"traffic_material_size",
		cat, show, click, ctrval = append2lists(feaTrafficMaterialSize, cat, show, click, ctrval)
		//"device_model"
		cat, show, click, ctrval = append2lists(feaDeviceModel, cat, show, click, ctrval)
		//"delivery_package", 就是TargetPackage
		cat, show, click, ctrval = append2lists(p.toNumFeaList(base, "delivery_package", mi.TargetPackage, true), cat, show, click, ctrval)

		// add to rc
		data.Data[i] = append(data.Data[i], cat...)
		data.Data[i] = append(data.Data[i], show...)
		data.Data[i] = append(data.Data[i], click...)
		data.Data[i] = append(data.Data[i], ctrval...)
	}
}

func (p *MixCtrCvrProcess) toNumFeaList(base *data.LightgbmBase, name, val string, needCat bool) (fr []float32) {
	fr = make([]float32, 0, 4)
	fr = append(fr, base.CategoryMap.GetVal(name, val))

	feasign := md5Hash(fmt.Sprintf("%s::%s", name, val))
	stat, exists := base.FeatureMap[feasign]

	if !exists {
		fr = append(fr, 0, 0)
	} else {
		fr = append(fr, float32(stat.ShowStats), float32(stat.ClickStats))
	}

	if !exists || stat.ShowStats < 10 {
		c := (stat.ClickStats + base.Alpha*base.Beta*base.PriorCTR) / (stat.ShowStats + base.Alpha*base.Beta)
		fr = append(fr, float32(c))
	} else {
		fr = append(fr, float32(stat.ClickStats/stat.ShowStats))
	}
	return
}
