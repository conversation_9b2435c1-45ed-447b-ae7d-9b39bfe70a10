package process

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"strconv"
	"time"
)

const (
	NameCtrV1        = "ctrv1"
	FeatureNum       = 210 // 暂时写到这里
	FeaAppStartIndex = 110
	FeatureListNum   = 28
	FeatureAppsNum   = 100
)

func init() {
	registry.RegisterFeaturesProcessing(NameCtrV1, &CtrV1Process{})
}

type CtrV1Process struct {
}

//channel_id,ad_id,creative_id,country,day_of_week,event_hour,
//device_brand,device_type,os,os_version,network_type,
//advertiser_type,tag_id,package_name,device_id_type,
//timezone,account_id,project_id,material_id_type,
//title_id,description_id,delivery_type,traffic_material_size,
//delivery_material_size,image_material_id,video_material_id

func (p *CtrV1Process) Init(config map[string]string) {
}

func (p *CtrV1Process) MachiningFeature(rc *models.RankContext) {
	defer tools.Recover()

	//TODO: 数据处理，尽量使用快速的方法
	impressionTime := time.Now().UTC()

	loc := time.FixedZone("Custom", tools.TimeOffset(rc.Request.DeviceInfo.CountryCode))
	impressionTime = impressionTime.In(loc)

	//layout := "2006-01-02 15:04:05"
	//impressionTime, _ = time.Parse(layout, "2025-03-24 16:10:14")

	dayOfWeek := impressionTime.Weekday().String()[:3]
	eventHour := fmt.Sprintf("%02d", impressionTime.Hour())
	// 保证顺序与rc.Request.AdMaterialInfo 相同
	var (
		cat     = make([]float32, 0, FeatureListNum)
		show    = make([]float32, 0, FeatureListNum)
		click   = make([]float32, 0, FeatureListNum)
		ctrval  = make([]float32, 0, FeatureListNum)
		applist = make([]float32, FeatureAppsNum)
	)

	rc.InputData.CtrData = &models.FloatMetrics[float32]{
		Row:  len(rc.Request.AdMaterialInfo),
		Col:  FeatureNum,
		Data: make([]float32, 0, len(rc.Request.AdMaterialInfo)*FeatureNum),
	}
	var (
		feaChannelId      = p.toNumFeaList(rc.CtrV1, "channel_id", rc.Request.DeviceInfo.ChannelId, true)
		feaCountry        = p.toNumFeaList(rc.CtrV1, "country", rc.Request.DeviceInfo.CountryCode, true)
		feaDayOfWeek      = p.toNumFeaList(rc.CtrV1, "day_of_week", dayOfWeek, true)
		feaEventHour      = p.toNumFeaList(rc.CtrV1, "event_hour", eventHour, true)
		feaDeviceBrand    = p.toNumFeaList(rc.CtrV1, "device_brand", rc.Request.DeviceInfo.Brand, true)
		feaDeviceType     = p.toNumFeaList(rc.CtrV1, "device_type", strconv.Itoa(int(rc.Request.DeviceInfo.DeviceType)), true)
		feaOs             = p.toNumFeaList(rc.CtrV1, "os", rc.Request.DeviceInfo.Os, true)
		feaOsVersion      = p.toNumFeaList(rc.CtrV1, "os_version", rc.Request.DeviceInfo.OsVersion, true)
		feaNetworkType    = p.toNumFeaList(rc.CtrV1, "network_type", strconv.Itoa(int(rc.Request.DeviceInfo.NetworkType)), true)
		feaAdvertiserType = p.toNumFeaList(rc.CtrV1, "advertiser_type", strconv.Itoa(int(rc.Request.AdSlotInfo.AdvertiserType)), true)
		feaAppId          = p.toNumFeaList(rc.CtrV1, "app_id", rc.Request.AdSlotInfo.SourcePackage, false)
		feaTagId          = p.toNumFeaList(rc.CtrV1, "tag_id", rc.Request.AdSlotInfo.AdSlotId, true)
		feaDeviceIdType   = p.toNumFeaList(rc.CtrV1, "device_id_type", rc.Request.DeviceInfo.DeviceIdType, true)
		//feaTimeZone            = p.toNumFeaList(rc.CtrV1, "timezone", rc.Request.DeviceInfo.Timezone, true)
		feaTrafficMaterialSize = p.toNumFeaList(rc.CtrV1, "traffic_material_size", rc.Request.AdSlotInfo.Size, true)
		feaDeviceModel         = p.toNumFeaList(rc.CtrV1, "device_model", rc.Request.DeviceInfo.Model, false)
	)

	var install []int32
	if rc.UserFeature != nil && len(rc.UserFeature.InstallList) > 0 {
		install = tools.MergeAndDeduplicate(rc.UserFeature.InstallList, rc.Request.DeviceInfo.InstallList)
	} else {
		install = rc.Request.DeviceInfo.InstallList
	}
	for _, v := range install {
		s := strconv.Itoa(int(v))
		if n, ok := rc.CtrV1.AppIndex[s]; ok {
			applist[n] = 1
		}
	}

	for _, mi := range rc.Request.AdMaterialInfo {
		cat = cat[:0]
		show = show[:0]
		click = click[:0]
		ctrval = ctrval[:0]
		//"channel_id"，
		cat, show, click, ctrval = append2lists(feaChannelId, cat, show, click, ctrval)
		//"ad_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "ad_id", mi.AdId, true), cat, show, click, ctrval)
		//"creative_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "creative_id", mi.CreativeGroupId, true), cat, show, click, ctrval)
		//"country",
		cat, show, click, ctrval = append2lists(feaCountry, cat, show, click, ctrval)
		//"day_of_week",
		cat, show, click, ctrval = append2lists(feaDayOfWeek, cat, show, click, ctrval)
		//"event_hour",
		cat, show, click, ctrval = append2lists(feaEventHour, cat, show, click, ctrval)
		//"device_brand",
		cat, show, click, ctrval = append2lists(feaDeviceBrand, cat, show, click, ctrval)
		//"device_type",
		cat, show, click, ctrval = append2lists(feaDeviceType, cat, show, click, ctrval)
		//"os",
		cat, show, click, ctrval = append2lists(feaOs, cat, show, click, ctrval)
		//"os_version",
		cat, show, click, ctrval = append2lists(feaOsVersion, cat, show, click, ctrval)
		//"network_type",
		cat, show, click, ctrval = append2lists(feaNetworkType, cat, show, click, ctrval)
		//"advertiser_type",  全是2
		cat, show, click, ctrval = append2lists(feaAdvertiserType, cat, show, click, ctrval)
		//"app_id", 无tag
		cat, show, click, ctrval = append2lists(feaAppId, cat, show, click, ctrval)
		//"tag_id",
		cat, show, click, ctrval = append2lists(feaTagId, cat, show, click, ctrval)
		//"package_name",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "package_name", mi.TargetPackage, true), cat, show, click, ctrval)
		//"device_id_type", 全是GPID
		cat, show, click, ctrval = append2lists(feaDeviceIdType, cat, show, click, ctrval)
		//"timezone", +00:00
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "timezone", mi.Timezone, true), cat, show, click, ctrval)
		//"account_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "account_id", strconv.Itoa(int(mi.AccountId)), true), cat, show, click, ctrval)
		//"project_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "project_id", strconv.Itoa(int(mi.ProjectId)), true), cat, show, click, ctrval)
		//"material_id_type",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "material_id_type", strconv.Itoa(int(mi.MaterialIdType)), true), cat, show, click, ctrval)
		//"title_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "title_id", mi.TitleId, true), cat, show, click, ctrval)
		//"description_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "description_id", mi.DescriptionId, true), cat, show, click, ctrval)
		//"delivery_type",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "delivery_type", strconv.Itoa(int(rc.Request.AdSlotInfo.DeliveryType)), true), cat, show, click, ctrval)
		//"traffic_material_size",
		cat, show, click, ctrval = append2lists(feaTrafficMaterialSize, cat, show, click, ctrval)
		//"delivery_material_size",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "delivery_material_size", mi.MaterialSize, true), cat, show, click, ctrval)
		//"image_material_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "image_material_id", mi.ImageMaterialId, true), cat, show, click, ctrval)
		//"video_material_id",
		cat, show, click, ctrval = append2lists(p.toNumFeaList(rc.CtrV1, "video_material_id", mi.VideoMaterialId, true), cat, show, click, ctrval)
		//"device_model",无tag
		cat, show, click, ctrval = append2lists(feaDeviceModel, cat, show, click, ctrval)

		// add to rc
		rc.InputData.CtrData.Data = append(rc.InputData.CtrData.Data, cat...)
		rc.InputData.CtrData.Data = append(rc.InputData.CtrData.Data, show...)
		rc.InputData.CtrData.Data = append(rc.InputData.CtrData.Data, click...)
		rc.InputData.CtrData.Data = append(rc.InputData.CtrData.Data, ctrval...)
		rc.InputData.CtrData.Data = append(rc.InputData.CtrData.Data, applist...)
	}

	return
}

func (p *CtrV1Process) toNumFeaList(ctr *data.CtrV1, name, val string, needCat bool) (fr []float32) {
	if needCat {
		fr = make([]float32, 0, 4)
		fr = append(fr, ctr.CategoryMap.GetVal(name, val))
	} else {
		fr = make([]float32, 0, 3)
	}

	feasign := md5Hash(fmt.Sprintf("%s::%s", name, val))
	stat, exists := ctr.FeatureMap[feasign]

	if !exists {
		fr = append(fr, 0, 0)
	} else {
		fr = append(fr, float32(stat.ShowStats), float32(stat.ClickStats))
	}

	if !exists || stat.ShowStats < 10 {
		c := (stat.ClickStats + ctr.Alpha*ctr.Beta*ctr.PriorCTR) / (stat.ShowStats + ctr.Alpha*ctr.Beta)
		fr = append(fr, float32(c))
	} else {
		fr = append(fr, float32(stat.ClickStats/stat.ShowStats))
	}
	return
}

func append2lists(fr []float32, cat []float32, show []float32, click []float32, ctr []float32) ([]float32, []float32, []float32, []float32) {
	if len(fr) == 4 {
		cat = append(cat, fr[0])
		show = append(show, fr[1])
		click = append(click, fr[2])
		ctr = append(ctr, fr[3])
	} else {
		show = append(show, fr[0])
		click = append(click, fr[1])
		ctr = append(ctr, fr[2])
	}
	return cat, show, click, ctr
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}
