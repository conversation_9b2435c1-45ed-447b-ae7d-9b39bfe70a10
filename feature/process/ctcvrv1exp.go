package process

import (
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
)

const (
	NameCtcvrV1exp = "ctcvrv1exp"
)

func init() {
	registry.RegisterFeaturesProcessing(NameCtcvrV1exp, &CtcvrV1ExpProcess{})
}

type CtcvrV1ExpProcess struct {
	expName string
}

func (p *CtcvrV1ExpProcess) Init(config map[string]string) {
	p.expName = models.AB20Exp7Copy
}

func (p *CtcvrV1ExpProcess) MachiningFeature(rc *models.RankContext) {
	defer tools.Recover()

	if rc.InputDataExp == nil {
		rc.InputDataExp = make(map[string]*models.InputMetrics)
	}
	if rc.InputDataExp[p.expName] == nil {
		rc.InputDataExp[p.expName] = &models.InputMetrics{}
	}
	rc.InputDataExp[p.expName].CtrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureNum),
	}
	rc.InputDataExp[p.expName].CvrData = &models.FloatMetrics[float32]{
		Row:  len(rc.ExpAds[p.expName]),
		Col:  FeatureNum,
		Data: make([]float32, 0, len(rc.ExpAds[p.expName])*FeatureNum),
	}

	fea2Data(rc, rc.CtrV1.LightgbmBase, rc.InputDataExp[p.expName].CtrData, p.expName)
	fea2Data(rc, rc.CvrV1.LightgbmBase, rc.InputDataExp[p.expName].CvrData, p.expName)

	return
}
