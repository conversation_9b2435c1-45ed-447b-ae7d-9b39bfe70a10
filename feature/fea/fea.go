package fea

import (
	"context"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/reader"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"sync"
	"time"
)

const (
	Name = "stdfea"
)

func init() {
	registry.RegisterFeaExtraction(Name, &StdFeatures{})
}

type StdFeatures struct {
	adProfile *redis.Client
}

func (s *StdFeatures) Init(config map[string]string) {
	s.adProfile = reader.ProfileRedisInstance()
}

const (
	BlackListId = "00000000-0000-0000-0000-000000000000"
	NullKey     = "null"
)

func (s *StdFeatures) GetFeatures(rc *models.RankContext) {
	if !apollo.ApolloRankerConfig().GetUserFeatureEnable() {
		return
	}
	// 过滤特殊id
	if rc.DeviceId == BlackListId || rc.DeviceId == NullKey {
		return
	}

	wg := sync.WaitGroup{}

	// 画像
	wg.Add(1)
	go func() {
		defer tools.Recover()
		funcName := "UserFeatures"
		status := functionlog.FuncStatusOK
		var message string
		defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()
		defer wg.Done()
		timeout := apollo.ApolloRankerConfig().GetUserFeatureTimeout()
		uf, err := s.GetUserFeature(rc.DeviceId, timeout)
		if err != nil {
			//status = functionlog.FuncStatusFailed
			logger.Instance().Error("GetUserVBU failed: %s, requestid = %s, deviceid = %s", err, rc.RequestId, rc.DeviceId)
			return
		}
		rc.UserFeature = uf
	}()

	wg.Wait()
}

func (s *StdFeatures) GetUserFeature(id string, timeout int) (*models.UserFeatures, error) {
	ctx, _ := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	b, err := s.adProfile.Get(ctx, id).Bytes()

	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return nil, err
		}
		return nil, nil
	}
	uf := &models.UserFeatures{}
	err = proto.Unmarshal(b, uf)
	if err != nil {
		return nil, err
	}
	return uf, nil
}
