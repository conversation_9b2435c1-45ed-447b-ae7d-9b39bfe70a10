package impl

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"strconv"
)

const (
	NameExplore = "trafficexplore"
	// apollo变量
	ImpressThreshold = 10000
)

func init() {
	registry.RegisterTrafficExplore(NameExplore, &TrafficExpl{})
}

type TrafficExpl struct {
}

func (e *TrafficExpl) Explore(rc *models.RankContext) {
	// implement me
	for i, material := range rc.Request.AdMaterialInfo {
		adIdStr := material.AdId
		adId, err := strconv.Atoi(adIdStr)
		if err != nil {
			fmt.Println("AdId转换失败:", err)
			continue
		}
		// 广告大于 曝光曝光阈值
		if data.IcInstance().GetImpress(adId) > ImpressThreshold {
			// process
			i = i
		} else {
			// process
			i = i + 1
		}

	}
	rc = rc
}

func (t *TrafficExpl) Init(c map[string]string) {

}
