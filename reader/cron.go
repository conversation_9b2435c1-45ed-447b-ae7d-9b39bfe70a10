package reader

import (
	"context"
	"github.com/redis/go-redis/v9"
	con "gitlab.ydmob.com/algorithm/brief-framework/config"
	"time"
)

func init() {
	adCronAddr := con.Instance().MustValue("cron-redis", "addr")
	adCronPwd := con.Instance().MustValue("cron-redis", "passwd")
	adCronSetTimeout := con.Instance().MustInt("cron-redis", "set_timeout", 500)
	adCronReadTimeout := con.Instance().MustInt("cron-redis", "read_timeout", 500)

	adCron := redis.NewClient(&redis.Options{
		Addr:         adCronAddr,
		Password:     adCronPwd,
		ReadTimeout:  time.Duration(adCronSetTimeout) * time.Millisecond,
		WriteTimeout: time.Duration(adCronReadTimeout) * time.Millisecond,
		PoolSize:     50,
		MinIdleConns: 50,
		MaxRetries:   1,
	})
	ctx := context.Background()
	_, err := adCron.Ping(ctx).Result()
	if err != nil {
		panic(err)
	}
	cron = adCron
}

var cron *redis.Client

func CronRedisInstance() *redis.Client {
	return cron
}
