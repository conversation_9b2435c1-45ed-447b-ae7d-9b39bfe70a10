package reader

import (
	"context"
	"github.com/redis/go-redis/v9"
	con "gitlab.ydmob.com/algorithm/brief-framework/config"
	"time"
)

func init() {
	adProfileAddr := con.Instance().MustValue("profile-redis", "addr")
	adProfilePwd := con.Instance().MustValue("profile-redis", "passwd")
	adProfileSetTimeout := con.Instance().MustInt("profile-redis", "set_timeout", 50)
	adProfileReadTimeout := con.Instance().MustInt("profile-redis", "read_timeout", 50)

	adProfile := redis.NewClient(&redis.Options{
		Addr:         adProfileAddr,
		Password:     adProfilePwd,
		ReadTimeout:  time.Duration(adProfileSetTimeout) * time.Millisecond,
		WriteTimeout: time.Duration(adProfileReadTimeout) * time.Millisecond,
		PoolSize:     300,
		MinIdleConns: 200,
		MaxRetries:   1,
	})
	ctx := context.Background()
	_, err := adProfile.Ping(ctx).Result()
	if err != nil {
		panic(err)
	}
	profile = adProfile
}

var profile *redis.Client

func ProfileRedisInstance() *redis.Client {
	return profile
}
