// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0-devel
// 	protoc        v3.8.0
// source: user_features.proto

package models

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义用户特征消息
type UserFeatures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 安装列表
	InstallList []int32 `protobuf:"varint,1,rep,packed,name=install_list,json=installList,proto3" json:"install_list,omitempty"`
	// 推荐包名
	RecommendedPackageIds []int32 `protobuf:"varint,2,rep,packed,name=recommended_package_ids,json=recommendedPackageIds,proto3" json:"recommended_package_ids,omitempty"`
	// 搜索推荐列表
	SearchRecommendations []int32 `protobuf:"varint,3,rep,packed,name=search_recommendations,json=searchRecommendations,proto3" json:"search_recommendations,omitempty"`
	// 性别 1:男 2:女, 0:未知
	Gender int32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 品牌id 详见枚举列表
	Brand int32 `protobuf:"varint,5,opt,name=brand,proto3" json:"brand,omitempty"`
	// 手机型号
	Model string `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *UserFeatures) Reset() {
	*x = UserFeatures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_features_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFeatures) ProtoMessage() {}

func (x *UserFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_user_features_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFeatures.ProtoReflect.Descriptor instead.
func (*UserFeatures) Descriptor() ([]byte, []int) {
	return file_user_features_proto_rawDescGZIP(), []int{0}
}

func (x *UserFeatures) GetInstallList() []int32 {
	if x != nil {
		return x.InstallList
	}
	return nil
}

func (x *UserFeatures) GetRecommendedPackageIds() []int32 {
	if x != nil {
		return x.RecommendedPackageIds
	}
	return nil
}

func (x *UserFeatures) GetSearchRecommendations() []int32 {
	if x != nil {
		return x.SearchRecommendations
	}
	return nil
}

func (x *UserFeatures) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UserFeatures) GetBrand() int32 {
	if x != nil {
		return x.Brand
	}
	return 0
}

func (x *UserFeatures) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

var File_user_features_proto protoreflect.FileDescriptor

var file_user_features_proto_rawDesc = []byte{
	0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x15, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x35, 0x0a, 0x16, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x15, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x0b, 0x5a, 0x09, 0x2e,
	0x2f, 0x3b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_features_proto_rawDescOnce sync.Once
	file_user_features_proto_rawDescData = file_user_features_proto_rawDesc
)

func file_user_features_proto_rawDescGZIP() []byte {
	file_user_features_proto_rawDescOnce.Do(func() {
		file_user_features_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_features_proto_rawDescData)
	})
	return file_user_features_proto_rawDescData
}

var file_user_features_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_user_features_proto_goTypes = []interface{}{
	(*UserFeatures)(nil), // 0: user_features.UserFeatures
}
var file_user_features_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_user_features_proto_init() }
func file_user_features_proto_init() {
	if File_user_features_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_features_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserFeatures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_features_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_features_proto_goTypes,
		DependencyIndexes: file_user_features_proto_depIdxs,
		MessageInfos:      file_user_features_proto_msgTypes,
	}.Build()
	File_user_features_proto = out.File
	file_user_features_proto_rawDesc = nil
	file_user_features_proto_goTypes = nil
	file_user_features_proto_depIdxs = nil
}
