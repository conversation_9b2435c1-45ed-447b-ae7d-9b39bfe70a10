package models

import (
	"strings"
	"time"

	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
)

type RankContext struct {
	RequestId string
	DeviceId  string
	//ModelName   string
	//Duration    time.Duration
	Request     *ranker.AdRequestData
	UserFeature *UserFeatures
	InputData   *InputMetrics
	PredictData *OutputMetrics
	Response    *ranker.AdResponseData
	CtrV1       *data.CtrV1
	CvrV1       *data.CvrV1
	Exps        map[string]bool
	NowTime     time.Time
	BiddingV1   *data.BiddingV1
	// 新版实验更改
	ExpAds         map[string][]*ranker.AdMaterial // 对广告进行实验的分组
	ExpNames       map[string][]string             // 对应ExpAds中的实验桶名
	InputDataExp   map[string]*InputMetrics
	PredictDataExp map[string]*OutputMetrics

	CtrCpa *data.CtrV1
	CvrCpa *data.CvrV1
}

func NewRankContext(req *ranker.AdRequestData) *RankContext {
	rc := &RankContext{
		RequestId:   req.RequestId,
		DeviceId:    req.DeviceInfo.DeviceId,
		Request:     req,
		InputData:   &InputMetrics{},
		PredictData: &OutputMetrics{},
		Response:    &ranker.AdResponseData{},
		Exps:        map[string]bool{},
		NowTime:     time.Now().UTC(),
	}
	tests := strings.Split(req.AbTest, ",")
	for _, v := range tests {
		rc.Exps[v] = true
	}
	return rc
}

type FloatMetrics[T float32 | float64] struct {
	Data []T
	Row  int
	Col  int
}

type FloatMetrics2[T float32 | float64] struct {
	Data [][]T
}

type InputMetrics struct {
	CtrData     *FloatMetrics[float32]
	CvrData     *FloatMetrics[float32]
	BiddingData *FloatMetrics2[float32]
}

type OutputMetrics struct {
	CtrData        *FloatMetrics[float64]
	CvrData        *FloatMetrics[float64]
	BiddingData    *FloatMetrics[float64]
	CtrCaliData    *FloatMetrics[float64]
	CvrCaliData    *FloatMetrics[float64]
	PredictBidData *FloatMetrics[float64]
	BiddingOriData *FloatMetrics[float64]
}
