syntax = "proto3";

package user_features;

option go_package = "./;models";

// 定义用户特征消息
message UserFeatures {
    // 安装列表
    repeated int32 install_list = 1;
    // 推荐包名
    repeated int32 recommended_package_ids = 2;
    // 搜索推荐列表
    repeated int32 search_recommendations = 3;
    // 性别 1:男 2:女, 0:未知
    int32 gender = 4;
    // 品牌id 详见枚举列表
    int32 brand = 5;
    // 手机型号
    string model = 6;
}