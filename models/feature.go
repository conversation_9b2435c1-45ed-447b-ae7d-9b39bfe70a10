package models

type AdMaterial struct {
	BigImg       string `json:"bigImg"`
	AdTemplateId string `json:"adTemplateId"`
	SeriesName   string `json:"seriesName"`
	RecallCarId  string `json:"recallCarId"`
	LevelName    string `json:"levelName"`
	Title        string `json:"title"`
	Content      string `json:"content"`
	ImgHyaline   string `json:"imgHyaline"`
	Price        string `json:"price"`
	MinPrice     string `json:"minPrice"`
	MaxPrice     string `json:"maxPrice"`
	Button       string `json:"button"`
	BottomLabel1 string `json:"bottomLabel1"`
	BottomLabel2 string `json:"bottomLabel2"`
	BottomLabel3 string `json:"bottomLabel3"`
	Ext          string `json:"ext"`
	CreativeId   int    `json:"creativeId"`
}

type FeatureItem struct {
	UserIs24h                 string   `json:"user_is_24h"`
	UserIs24hTo7d             string   `json:"user_is_24h_to_7d"`
	UserIs7dTo14d             string   `json:"user_is_7d_to_14d"`
	UserIs1m                  string   `json:"user_is_1m"`
	UserIs1yAgo               string   `json:"user_is_1y_ago"`
	BaseOftenCity             string   `json:"base_often_city"`      //省
	BaseOftenCityArea         string   `json:"base_often_city_area"` //市
	UserIsFirstCityLevel      string   `json:"user_is_first_city_level"`
	BaseOftenCityLevel        string   `json:"base_often_city_level"`
	UserReqCity               string   `json:"user_req_city"`      //省
	UserReqCityArea           string   `json:"user_req_city_area"` //市
	BaseSex                   string   `json:"base_sex"`
	BaseAgeGroup              string   `json:"base_age_group"`
	BaseDeviceOs              string   `json:"base_device_os"`
	UserVBUStage              string   `json:"user_vbu_stage"`
	UserLifeCycle             string   `json:"user_life_cycle"`
	UserRFMGroup              string   `json:"user_rfm_group"`
	UserAutohomeAge           string   `json:"user_autohome_age"`
	UserViewcarSeriList       []string `json:"user_viewcar_serilist"`
	UserViewcarBrandList      []string `json:"user_viewcar_brandlist"`
	UserViewcarManuList       []string `json:"user_viewcar_manulist"`
	UserSearchcarSeriList     []string `json:"user_searchcar_serilist"`
	UserSearchcarBrandList    []string `json:"user_searchcar_brandlist"`
	UserBuycarSeriList        []string `json:"user_buycar_serilist"`
	UserBuycarBrandList       []string `json:"user_buycar_brandlist"`
	UserBuycarManuList        []string `json:"user_buycar_manulist"`
	UserBuycarCarpriceList    []string `json:"user_buycar_carpricelist"`
	UserKeywordsList          []string `json:"user_keywordslist"`
	UserCityBrandSalesList    []string `json:"user_city_brand_saleslist"`
	UserCitySeriesSalesList   []string `json:"user_city_series_saleslist"`
	UserHistSearQuery         []string `json:"user_hist_sear_query"`       // 兴趣中台数据
	UserHistSearBrandName     []string `json:"user_hist_sear_brand_name"`  // 兴趣中台数据
	UserHistSearSeriesName    []string `json:"user_hist_sear_series_name"` // 兴趣中台数据
	UserHistCarSeriesid       []string `json:"user_hist_car_seriesid"`     // 兴趣中台数据
	QueryKeyword              string   `json:"query_keyword"`
	QSeriesName               string   `json:"q_series_name"`
	QSeriesId                 string   `json:"q_series_id"`
	QIntent                   string   `json:"q_intent"`
	QVehicleName              string   `json:"q_vehicle_name"`
	QVehicleId                string   `json:"q_vehicle_id"`
	QBrandName                string   `json:"q_brand_name"`
	QBrandId                  string   `json:"q_brand_id"`
	QFactoryName              string   `json:"q_factory_name"`
	QFactoryId                string   `json:"q_factory_id"`
	QSeriesIsImport           string   `json:"q_series_is_import"`
	QSeriesPlace              string   `json:"q_series_place"`
	QCarLevelId               string   `json:"q_car_level_id"`
	QCarLevelName             string   `json:"q_car_level_name"`
	QCountryId                string   `json:"q_country_id"`
	QCountryName              string   `json:"q_country_name"`
	QSeriesIsPublic           string   `json:"q_series_is_public"`
	QSeriesState              string   `json:"q_series_state"`
	QSeriesIsNewEnergy        string   `json:"q_series_is_new_energy"`
	QSeriesGuidePriceMin      string   `json:"q_series_guide_price_min"`
	QSeriesGuidePriceMax      string   `json:"q_series_guide_price_max"`
	QSeriesDealerPriceMin     string   `json:"q_series_dealer_price_min"`
	QSeriesDealerPriceMax     string   `json:"q_series_dealer_price_max"`
	QSeriesSalecnt            string   `json:"q_series_salecnt"`
	QSeriesSalern             string   `json:"q_series_salern"`
	QSeriesPreSalecnt         string   `json:"q_series_pre_salecnt"`
	QSeriesPreSalern          string   `json:"q_series_pre_salern"`
	Weekday                   string   `json:"weekday"`
	WeekIndex                 string   `json:"weekindex"`
	AdEnv                     string   `json:"ad_env"`
	AdTemplateId              string   `json:"ad_template_id"`
	AdCreativeId              string   `json:"ad_creative_id"`
	MatchKeywords             string   `json:"match_keywords"`
	AdSeriesName              string   `json:"ad_series_name"`
	AdSeriesId                string   `json:"ad_series_id"`
	AdBrandName               string   `json:"ad_brand_name"`
	AdBrandId                 string   `json:"ad_brand_id"`
	AdFactoryName             string   `json:"ad_factory_name"`
	AdFactoryId               string   `json:"ad_factory_id"`
	AdSeriesIsImport          string   `json:"ad_series_is_import"`
	AdSeriesPlace             string   `json:"ad_series_place"`
	AdCarLevelId              string   `json:"ad_car_level_id"`
	AdCarLevelName            string   `json:"ad_car_level_name"`
	AdCountryId               string   `json:"ad_country_id"`
	AdCountryName             string   `json:"ad_country_name"`
	AdSeriesIsPublic          string   `json:"ad_series_is_public"`
	AdSeriesState             string   `json:"ad_series_state"`
	AdSeriesIsNewEnergy       string   `json:"ad_series_is_new_energy"`
	AdSeriesGuidePriceMin     string   `json:"ad_series_guide_price_min"`
	AdSeriesGuidePriceMax     string   `json:"ad_series_guide_price_max"`
	AdSeriesDealerPriceMin    string   `json:"ad_series_dealer_price_min"`
	AdSeriesDealerPriceMax    string   `json:"ad_series_dealer_price_max"`
	AdPriceMin                string   `json:"ad_price_min"`
	AdPriceMax                string   `json:"ad_price_max"`
	AdButton                  string   `json:"ad_button"`
	AdBottomLabel1            string   `json:"ad_bottom_label1"`
	AdBottomLabel2            string   `json:"ad_bottom_label2"`
	AdBottomLabel3            string   `json:"ad_bottom_label3"`
	AdSubsidy                 string   `json:"ad_subsidy"`
	AdSubsidyGuideRate        string   `json:"ad_subsidy_guide_rate"`
	AdSubsidyDealerRate       string   `json:"ad_subsidy_dealer_rate"`
	AdDiscount                string   `json:"ad_discount"`
	AdDiscountGuideRate       string   `json:"ad_discount_guide_rate"`
	AdDiscountDealerRate      string   `json:"ad_discount_dealer_rate"`
	AdRightsDiscount          string   `json:"ad_rights_discount"`
	AdRightsSubsidy           string   `json:"ad_rights_subsidy"`
	AdRightsReplaceSubsidy    string   `json:"ad_rights_replace_subsidy"`
	AdRightsReserve           string   `json:"ad_rights_reserve"`
	AdTitle                   string   `json:"ad_title"`
	AdContent                 string   `json:"ad_content"`
	AdSeriesSalecnt           string   `json:"ad_series_salecnt"`
	AdSeriesSalern            string   `json:"ad_series_salern"`
	AdSeriesPreSalecnt        string   `json:"ad_series_pre_salecnt"`
	AdSeriesPreSalern         string   `json:"ad_series_pre_salern"`
	MatchQAdSeriesId          string   `json:"match_q_ad_series_id"` //删掉
	MatchQAdLevelId           string   `json:"match_q_ad_level_id"`
	MatchQAdNewEnergy         string   `json:"match_q_ad_new_energy"`
	MatchQAdPriceLevel        string   `json:"match_q_ad_price_level"`
	MatchQAdPriceDiff         string   `json:"match_q_ad_price_diff"`
	MatchAdPriceLtQPrice      string   `json:"match_ad_price_lt_q_price"`
	MatchAdSalernLtQSalern    string   `json:"match_ad_salern_lt_q_salern"`
	MatchQAdSalecntLevel      string   `json:"match_q_ad_salecnt_level"`
	MatchAdInterestSeriesTop1 string   `json:"match_ad_interest_series_top1"`
	MatchAdInterestSeriesTop2 string   `json:"match_ad_interest_series_top2"`
	MatchAdInterestSeriesTop3 string   `json:"match_ad_interest_series_top3"`
	MatchAdInterestSeries     string   `json:"match_ad_interest_series"`
	MatchQInterestSeriesTop1  string   `json:"match_q_interest_series_top1"`
	MatchQInterestSeriesTop2  string   `json:"match_q_interest_series_top2"`
	MatchQInterestSeriesTop3  string   `json:"match_q_interest_series_top3"`
	MatchQInterestSeries      string   `json:"match_q_interest_series"`
	MatchAdPriceSeries        string   `json:"match_ad_price_series"`
	MatchQPriceSeries         string   `json:"match_q_price_series"`
	MatchAdSeriesCityTop1     string   `json:"match_ad_series_city_top1"`
	MatchAdSeriesCityTop2     string   `json:"match_ad_series_city_top2"`
	MatchAdSeriesCityTop3     string   `json:"match_ad_series_city_top3"`
	MatchAdSeriesCity         string   `json:"match_ad_series_city"`
	MatchQSeriesCityTop1      string   `json:"match_q_series_city_top1"`
	MatchQSeriesCityTop2      string   `json:"match_q_series_city_top2"`
	MatchQSeriesCityTop3      string   `json:"match_q_series_city_top3"`
	MatchQSeriesCity          string   `json:"match_q_series_city"`
}

func NewFeatureItem() *FeatureItem {
	// 对所有的值赋值默认值
	return &FeatureItem{
		UserIs24h:                 "-1",
		UserIs24hTo7d:             "-1",
		UserIs7dTo14d:             "-1",
		UserIs1m:                  "-1",
		UserIs1yAgo:               "-1",
		BaseOftenCity:             "-1",
		BaseOftenCityArea:         "-1",
		UserIsFirstCityLevel:      "-1",
		BaseOftenCityLevel:        "-1",
		BaseSex:                   "-1",
		BaseAgeGroup:              "-1",
		BaseDeviceOs:              "-1",
		UserVBUStage:              "-1",
		UserLifeCycle:             "-1",
		UserRFMGroup:              "-1",
		UserAutohomeAge:           "-1",
		UserViewcarSeriList:       []string{"-1"},
		UserViewcarBrandList:      []string{"-1"},
		UserViewcarManuList:       []string{"-1"},
		UserSearchcarSeriList:     []string{"-1"},
		UserSearchcarBrandList:    []string{"-1"},
		UserBuycarSeriList:        []string{"-1"},
		UserBuycarBrandList:       []string{"-1"},
		UserBuycarManuList:        []string{"-1"},
		UserBuycarCarpriceList:    []string{"-1"},
		UserKeywordsList:          []string{"-1"},
		UserCityBrandSalesList:    []string{"-1"},
		UserCitySeriesSalesList:   []string{"-1"},
		UserHistSearQuery:         []string{"-1"},
		UserHistSearBrandName:     []string{"-1"},
		UserHistSearSeriesName:    []string{"-1"},
		UserHistCarSeriesid:       []string{"-1"},
		QueryKeyword:              "-1",
		QSeriesName:               "-1",
		QSeriesId:                 "-1",
		QIntent:                   "-1",
		QVehicleName:              "-1",
		QVehicleId:                "-1",
		QBrandName:                "-1",
		QBrandId:                  "-1",
		QFactoryName:              "-1",
		QFactoryId:                "-1",
		QSeriesIsImport:           "-1",
		QSeriesPlace:              "-1",
		QCarLevelId:               "-1",
		QCarLevelName:             "-1",
		QCountryId:                "-1",
		QCountryName:              "-1",
		QSeriesIsPublic:           "-1",
		QSeriesState:              "-1",
		QSeriesIsNewEnergy:        "-1",
		QSeriesGuidePriceMin:      "-1",
		QSeriesGuidePriceMax:      "-1",
		QSeriesDealerPriceMin:     "-1",
		QSeriesDealerPriceMax:     "-1",
		QSeriesSalecnt:            "-1",
		QSeriesSalern:             "-1",
		QSeriesPreSalecnt:         "-1",
		QSeriesPreSalern:          "-1",
		Weekday:                   "-1",
		WeekIndex:                 "-1",
		AdEnv:                     "-1",
		AdTemplateId:              "-1",
		AdCreativeId:              "-1",
		AdSeriesName:              "-1",
		AdSeriesId:                "-1",
		AdBrandName:               "-1",
		AdBrandId:                 "-1",
		AdFactoryName:             "-1",
		AdFactoryId:               "-1",
		AdSeriesIsImport:          "-1",
		AdSeriesPlace:             "-1",
		AdCarLevelId:              "-1",
		AdCarLevelName:            "-1",
		AdCountryId:               "-1",
		AdCountryName:             "-1",
		AdSeriesIsPublic:          "-1",
		AdSeriesState:             "-1",
		AdSeriesIsNewEnergy:       "-1",
		AdSeriesGuidePriceMin:     "-1",
		AdSeriesGuidePriceMax:     "-1",
		AdSeriesDealerPriceMin:    "-1",
		AdSeriesDealerPriceMax:    "-1",
		AdPriceMin:                "-1",
		AdPriceMax:                "-1",
		AdButton:                  "-1",
		AdBottomLabel1:            "-1",
		AdBottomLabel2:            "-1",
		AdBottomLabel3:            "-1",
		AdSubsidy:                 "-1",
		AdSubsidyGuideRate:        "-1",
		AdSubsidyDealerRate:       "-1",
		AdDiscount:                "-1",
		AdDiscountGuideRate:       "-1",
		AdDiscountDealerRate:      "-1",
		AdRightsDiscount:          "-1",
		AdRightsSubsidy:           "-1",
		AdRightsReplaceSubsidy:    "-1",
		AdRightsReserve:           "-1",
		AdTitle:                   "-1",
		AdContent:                 "-1",
		AdSeriesSalecnt:           "-1",
		AdSeriesSalern:            "-1",
		AdSeriesPreSalecnt:        "-1",
		AdSeriesPreSalern:         "-1",
		MatchQAdSeriesId:          "-1",
		MatchQAdLevelId:           "-1",
		MatchQAdNewEnergy:         "-1",
		MatchAdInterestSeries:     "-1",
		MatchQInterestSeries:      "-1",
		MatchAdPriceSeries:        "-1",
		MatchQPriceSeries:         "-1",
		MatchAdSeriesCity:         "-1",
		MatchQSeriesCity:          "-1",
		MatchQAdPriceLevel:        "-1",
		MatchQAdPriceDiff:         "-1",
		MatchAdPriceLtQPrice:      "-1",
		MatchAdSalernLtQSalern:    "-1",
		MatchQAdSalecntLevel:      "-1",
		MatchAdInterestSeriesTop1: "-1",
		MatchAdInterestSeriesTop2: "-1",
		MatchAdInterestSeriesTop3: "-1",
		MatchQInterestSeriesTop1:  "-1",
		MatchQInterestSeriesTop2:  "-1",
		MatchQInterestSeriesTop3:  "-1",
		MatchAdSeriesCityTop1:     "-1",
		MatchAdSeriesCityTop2:     "-1",
		MatchAdSeriesCityTop3:     "-1",
		MatchQSeriesCityTop1:      "-1",
		MatchQSeriesCityTop2:      "-1",
		MatchQSeriesCityTop3:      "-1",
		MatchKeywords:             "-1",
	}
}
