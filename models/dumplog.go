package models

type PredictionLog struct {
	Timestamp           int64   `json:"timestamp"`
	AdId                string  `json:"ad_id"`
	CtrScore            float64 `json:"ctr_score"`
	CvrScore            float64 `json:"cvr_score"`
	BidId               string  `json:"bid_id"`
	ChannelId           string  `json:"channel_id"`
	Country             string  `json:"country"`
	DeviceBrand         string  `json:"device_brand"`
	DeviceType          int32   `json:"device_type"`
	Os                  string  `json:"os"`
	OsVersion           string  `json:"os_version"`
	NetworkType         int32   `json:"network_type"`
	AdvertiserType      int32   `json:"advertiser_type"`
	AppId               string  `json:"app_id"`
	TagId               string  `json:"tag_id"`
	DeviceId            string  `json:"device_id"`
	DeviceIdType        string  `json:"device_id_type"`
	TrafficMaterialSize string  `json:"traffic_material_size"`
	DeviceModel         string  `json:"device_model"`
	// ctrv1.go
	// user feature
	Gender             int32   `json:"gender"`
	Ip                 string  `json:"ip"`
	InstallList        []int32 `json:"install_list"`
	RecommendedPackage []int32 `json:"recommended_package"`
	SearchRecommends   []int32 `json:"search_recommendations"`
	AbTest             string  `json:"ab_test"`

	CreativeGroupId string  `json:"creative_id"`
	TargetPackage   string  `json:"package_name"`
	Timezone        string  `json:"timezone"`
	AccountId       int32   `json:"account_id"`
	ProjectId       int32   `json:"project_id"`
	MaterialType    int32   `json:"material_id_type"`
	TitleId         string  `json:"title_id"`
	DescriptionId   string  `json:"description_id"`
	DeliveryMethod  int32   `json:"delivery_type"`
	MaterialSize    string  `json:"delivery_material_size"`
	ImageMaterialId string  `json:"image_material_id"`
	VideoMaterialId string  `json:"video_material_id"`
	Cpa             float64 `json:"cpa"`
	CaliCtr         float64 `json:"cali_ctr"`
	CaliCvr         float64 `json:"cali_cvr"`
	BidOri          float64 `json:"bid_ori"`
	BidModel        float64 `json:"bid_model"`
	BidPredic       float64 `json:"bid_predict"`
	AdUid           string  `json:"ad_uid"`
}
