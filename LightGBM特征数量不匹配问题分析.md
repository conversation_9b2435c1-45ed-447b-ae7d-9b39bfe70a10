# LightGBM特征数量不匹配问题分析

## 问题描述

在测试 `/server/rankv2.go` 第50行流程时遇到错误：
- **错误位置**：第67行CVR预测
- **错误信息**：`predict cvr error, code: 4294967295`
- **LightGBM错误**：`The number of features in data (212) is not the same as it was in training data (228)`

## 问题根本原因

### 1. 特征数量定义不匹配

```go
// ctcvrv3.go - 标准特征处理器
const FeatureAddedFeaNum = 212    // V3版本特征数量

// ctcvrc3CPA.go - CPA特征处理器  
const FeatureCPAAddedFeaNum = 228 // CPA版本特征数量（212 + 16个新特征）
```

### 2. 模型选择逻辑

在 `rankv2.go` 第58-65行的逻辑：

```go
for s := range rc.ExpAds {
    if strings.HasSuffix(s, models.CPASuffix) {
        // CPA广告：使用228特征的处理器和模型
        fp = registry.GetFeaturesProcessing(process.NameCtcvrCPAGroup)
        predict = registry.GetPredict(lightgbm.NameCPA)
    } else {
        // 普通广告：使用212特征的处理器和模型
        fp = registry.GetFeaturesProcessing(process.NameCtcvrV3)
        predict = registry.GetPredict(lightgbm.NameCtcvr3Exp)
    }
    fp.MachiningFeature(rc)  // 生成特征
    err := predict.Predict(rc) // 预测 - 这里出错
}
```

### 3. 可能的错误场景

1. **模型文件不匹配**：CPA模型文件期望228特征，但实际输入了212特征
2. **特征处理器选择错误**：可能使用了错误的特征处理器
3. **模型加载问题**：可能加载了错误版本的模型文件

## 解决方案

### 方案1：添加调试日志（推荐）

在第66行之前添加调试信息：

```go
fp.MachiningFeature(rc)

// 添加调试日志
logger.Instance().Info("Debug: expName=%s, featureProcessor=%T, predictor=%T", 
    s, fp, predict)
if rc.InputDataExp != nil && rc.InputDataExp[s] != nil {
    if rc.InputDataExp[s].CvrData != nil {
        logger.Instance().Info("Debug: CVR features count=%d, expected for CPA=%d, expected for V3=%d", 
            rc.InputDataExp[s].CvrData.Col, 228, 212)
    }
}

err := predict.Predict(rc)
```

### 方案2：强制特征数量检查

```go
fp.MachiningFeature(rc)

// 检查特征数量是否匹配
if strings.HasSuffix(s, models.CPASuffix) {
    // CPA广告应该有228个特征
    if rc.InputDataExp[s].CvrData.Col != 228 {
        logger.Instance().Error("CPA feature count mismatch: got %d, expected 228", 
            rc.InputDataExp[s].CvrData.Col)
        return rc.Response, fmt.Errorf("CPA feature count mismatch")
    }
} else {
    // 普通广告应该有212个特征
    if rc.InputDataExp[s].CvrData.Col != 212 {
        logger.Instance().Error("V3 feature count mismatch: got %d, expected 212", 
            rc.InputDataExp[s].CvrData.Col)
        return rc.Response, fmt.Errorf("V3 feature count mismatch")
    }
}

err := predict.Predict(rc)
```

### 方案3：检查模型文件

确认以下模型文件是否正确：
- CPA模型：`./depend/modelfile/cpacvr/cpa_local_cvr_model.txt`
- V3模型：`./depend/modelfile/cvr/local_cvr_model.txt`

## 调试步骤

1. **确认测试数据**：
   - 检查测试的广告名称是否包含 `CPASuffix`
   - 确认 `rc.ExpAds` 中的键值

2. **检查模型加载**：
   - 确认CPA模型和V3模型都正确加载
   - 检查模型期望的特征数量

3. **验证特征生成**：
   - 确认特征处理器生成的特征数量
   - 检查特征处理逻辑是否正确

## 临时解决方案

如果需要快速解决，可以在LightGBM预测时设置 `predict_disable_shape_check=true`，但这只是绕过检查，不解决根本问题。

**不推荐这种方式**，因为特征不匹配会导致预测结果不准确。

## 建议的修复顺序

1. 先添加调试日志，确认问题具体出现在哪里
2. 检查测试数据和广告名称
3. 验证模型文件和特征处理器的匹配性
4. 根据调试结果进行针对性修复

## 实际问题和修复

### 发现的真实问题

经过进一步分析，发现问题不是特征数量不匹配，而是 **ExpAds 键值不匹配**：

1. **ExpAds 设置**：根据 `ModelGroup="2"` 设置 `rc.ExpAds["2"]`
2. **Oppo 处理**：特征处理器和预测器期望 key 为 `"oppo-1004916"`
3. **数据访问失败**：预测器访问 `rc.InputDataExp["oppo-1004916"]` 但数据为空

### 修复方案

在 Oppo 处理逻辑中重新映射 ExpAds：

```go
// 重新映射 ExpAds 到 Oppo 的 key
oppoKey := req.AdSlotInfo.AdSlotId // "oppo-1004916" 或 "oppo-1004804"
if len(rc.ExpAds) > 0 {
    // 将所有现有的广告数据映射到 oppo key
    var allAds []*ranker.AdMaterial
    var allNames []string
    for key, ads := range rc.ExpAds {
        allAds = append(allAds, ads...)
        allNames = append(allNames, rc.ExpNames[key]...)
    }
    // 清空原有数据，重新设置到 oppo key
    rc.ExpAds = make(map[string][]*ranker.AdMaterial)
    rc.ExpNames = make(map[string][]string)
    rc.ExpAds[oppoKey] = allAds
    rc.ExpNames[oppoKey] = allNames
}
```

### 修复的文件

1. **server/rankv2.go**：添加 ExpAds 重新映射逻辑
2. **predict/lightgbm/ctcvrOppo.go**：修正 expName 为 TagIdOppo1

## 第二个问题：rankv1.go 中的 "bidding data is nil" 错误

### 问题描述

请求数据：
- `"ab_test_ad": "84_271_5"` → `exp = "5_cpa"`
- `"ad_slot_id": "oppo-1004804"`
- 错误：`"bidding data is nil"`

### 问题分析

1. **ExpAds 设置**：`rc.ExpAds["5_cpa"]` 被设置
2. **实验匹配**：`"5_cpa"` 匹配 `models.CPAExp`，进入 Oppo 处理
3. **ExpAds 重新映射**：数据被映射到 `rc.ExpAds["oppo-1004804"]`
4. **特征处理器问题**：`CtcvrOppoProcess.expName = "oppo-1004916"`，但实际数据在 `"oppo-1004804"`
5. **BiddingData 为空**：因为 `len(rc.ExpAds["oppo-1004916"]) = 0`

### 修复方案

1. **server/rankv1.go**：添加 ExpAds 重新映射逻辑（与 rankv2.go 相同）
2. **feature/process/ctcvrOppo.go**：动态查找正确的 Oppo key
3. **predict/lightgbm/ctcvrOppo.go**：动态查找正确的 Oppo key

### 修复的文件（第二轮）

1. **server/rankv1.go**：添加 ExpAds 重新映射逻辑
2. **feature/process/ctcvrOppo.go**：动态 expName 查找
3. **predict/lightgbm/ctcvrOppo.go**：动态 expName 查找
