package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
)

const (
	Name = "lightgbmpredict"
)

func init() {
	registry.RegisterSearchPredict(Name, &LightgbmPredictor{})
}

type LightgbmPredictor struct {
}

func (l *LightgbmPredictor) Init(config map[string]string) {
	//gl.InitializeLightgbm("./depend/lib_lightgbm.so")
}

func (l *LightgbmPredictor) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputData == nil || rc.InputData.CtrData == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputData.CtrData.Row), int32(rc.InputData.CtrData.Col)
	outval := make([]float64, row)
	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CtrV1.Model, rc.InputData.CtrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, outval)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict error, code: %d", cErr)
		return fmt.Errorf("predict error, code: %d", cErr)
	}

	if rc.CtrV1.NegSampleRate > 0.000001 {
		nsr := rc.CtrV1.NegSampleRate
		for i, v := range outval {
			outval[i] = v / (v + (1-v)/nsr)
		}
	}

	rc.PredictData.CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	//if rc.Exps[models.AB20Exp3] {
	calcCtrVal := make([]float64, row)
	for i, v := range outval {
		calcCtrVal[i] = rc.CtrV1.IsotonicModel.Predict(v)
	}
	rc.PredictData.CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}
	//}

	// 目前cvr数据是从redis获取的定时数据
	cvrArr := make([]float64, row)

	for i, mi := range rc.Request.AdMaterialInfo {
		cvrArr[i] = data.ScInstance().Cvr(rc.Request.AdSlotInfo.AdSlotId, mi.AdId)
	}

	rc.PredictData.CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	return nil
}
