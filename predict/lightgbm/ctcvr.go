package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
)

const (
	NameCtcvr = "lightgbmctcvr"
)

func init() {
	registry.RegisterSearchPredict(NameCtcvr, &LightgbmCtcvr{})
}

type LightgbmCtcvr struct {
}

func (l *LightgbmCtcvr) Init(config map[string]string) {
	//gl.InitializeLightgbm("./depend/lib_lightgbm.so")
}

func (l *LightgbmCtcvr) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict_ctcvr"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputData == nil || rc.InputData.CtrData == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputData.CtrData.Row), int32(rc.InputData.CtrData.Col)
	outval := make([]float64, row)
	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CtrV1.Model, rc.InputData.CtrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, outval)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict ctr error, code: %d", cErr)
		return fmt.Errorf("predict ctr error, code: %d", cErr)
	}
	if rc.CtrV1.NegSampleRate > 0.000001 {
		nsr := rc.CtrV1.NegSampleRate
		for i, v := range outval {
			outval[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCtrVal := make([]float64, row)
	for i, v := range outval {
		calcCtrVal[i] = rc.CtrV1.IsotonicModel.Predict(v)
	}
	rc.PredictData.CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}

	rc.PredictData.CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	row, col = int32(rc.InputData.CvrData.Row), int32(rc.InputData.CvrData.Col)
	cvrArr := make([]float64, row)

	cErr = gl.BoosterPredictForMat(rc.CvrV1.Model, rc.InputData.CvrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, cvrArr)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict cvr error, code: %d", cErr)
		return fmt.Errorf("predict cvr error, code: %d", cErr)
	}
	if rc.CvrV1.NegSampleRate > 0.000001 {
		nsr := rc.CvrV1.NegSampleRate
		for i, v := range cvrArr {
			cvrArr[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCvrVal := make([]float64, row)
	for i, v := range cvrArr {
		calcCvrVal[i] = rc.CvrV1.IsotonicModel.Predict(v)
	}
	rc.PredictData.CvrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCvrVal,
	}

	rc.PredictData.CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	return nil
}
