package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
)

const (
	NameCtrExp = "ctrexp"
)

func init() {
	registry.RegisterSearchPredict(NameCtrExp, &LightgbmCtrExp{})
}

type LightgbmCtrExp struct {
	expName string
}

func (l *LightgbmCtrExp) Init(config map[string]string) {
	l.expName = models.AB20Exp6Copy
}

func (l *LightgbmCtrExp) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputDataExp[l.expName].CtrData.Row), int32(rc.InputDataExp[l.expName].CtrData.Col)
	outval := make([]float64, row)
	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CtrV1.Model, rc.InputDataExp[l.expName].CtrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, outval)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict error, code: %d", cErr)
		return fmt.Errorf("predict error, code: %d", cErr)
	}

	if rc.CtrV1.NegSampleRate > 0.000001 {
		nsr := rc.CtrV1.NegSampleRate
		for i, v := range outval {
			outval[i] = v / (v + (1-v)/nsr)
		}
	}

	if rc.PredictDataExp == nil {
		rc.PredictDataExp = make(map[string]*models.OutputMetrics)
	}

	if rc.PredictDataExp[l.expName] == nil {
		rc.PredictDataExp[l.expName] = &models.OutputMetrics{}
	}

	rc.PredictDataExp[l.expName].CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	calcCtrVal := make([]float64, row)
	for i, v := range outval {
		calcCtrVal[i] = rc.CtrV1.IsotonicModel.Predict(v)
	}
	rc.PredictDataExp[l.expName].CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}

	// 目前cvr数据是从redis获取的定时数据
	cvrArr := make([]float64, row)

	for i, mi := range rc.ExpAds[l.expName] {
		cvrArr[i] = data.ScInstance().Cvr(rc.Request.AdSlotInfo.AdSlotId, mi.AdId)
	}

	rc.PredictDataExp[l.expName].CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	return nil
}
