package lightgbm

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"math"
	"strconv"
)

const (
	BidIndex1 = 12
	BidIndex2 = 12 + 27
	BidIndex3 = 12 + 27 + 27
	BidIndex4 = 12 + 27 + 27 + 27

	BidFeaNum = 108

	GoldenSection        = 1.618033989
	GoldenSectionIterNum = 5
	GoldenSectionEpsilon = 1.0
)

const (
	NameBidding = "lightgbmbidding"
)

func init() {
	registry.RegisterSearchPredict(NameBidding, &LightgbmBidding{})
}

type LightgbmBidding struct {
}

func (l *LightgbmBidding) Init(config map[string]string) {
	//gl.InitializeLightgbm("./depend/lib_lightgbm.so")
}

func (l *LightgbmBidding) Predict(rc *models.RankContext) error {

	cvrPredict := registry.GetPredict(NameCtcvr)
	err := cvrPredict.Predict(rc)
	if err != nil {
		logger.Instance().Error("Predict err %v, requestid = %s", err, rc.RequestId)
		return err
	}

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict_bidding"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputData == nil || rc.InputData.BiddingData == nil {
		return fmt.Errorf("bidding data is nil")
	}
	if len(rc.InputData.BiddingData.Data) == 0 {
		return fmt.Errorf("bidding data is nil")
	}

	rc.PredictData.BiddingData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputData.BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputData.BiddingData.Data)),
	}
	rc.PredictData.PredictBidData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputData.BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputData.BiddingData.Data)),
	}

	bidding := NewBidding(rc.BiddingV1, rc.InputData.BiddingData.Data[0], rc.BiddingV1.FeatureMap)
	defer gl.FastConfigFree(bidding.fastConfig)

	for i, v := range rc.InputData.BiddingData.Data {
		bidding.SetFea(v)
		ctr, cvr := rc.PredictData.CtrData.Data[i], rc.PredictData.CvrData.Data[i]
		if rc.PredictData.CtrCaliData != nil {
			ctr = rc.PredictData.CtrCaliData.Data[i]
		}
		if rc.PredictData.CvrCaliData != nil {
			cvr = rc.PredictData.CvrCaliData.Data[i]
		}
		bidOri := rc.Request.AdMaterialInfo[i].Cpa * ctr * cvr * 1000
		roundBid := math.Round(bidOri)
		rc.PredictData.PredictBidData.Data[i] = roundBid
		if roundBid <= 1 {
			rc.PredictData.BiddingData.Data[i] = roundBid
		} else {
			start := max(1, float32(bidOri/2))
			rc.PredictData.BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), start, float32(bidOri)))
			//rc.PredictData.BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri)))
			//if rc.Exps[models.AB20Exp8] {
			//	rc.PredictData.BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri)))
			//} else if rc.Exps[models.AB20Exp9] {
			//	start := max(1, float32(bidOri/2))
			//	rc.PredictData.BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), start, float32(bidOri)))
			//}
		}
	}

	return nil
}

type Bidding struct {
	fea        []float32
	fastConfig gl.FastConfig
	feaSignMap map[string]data.Feature
	Model      *data.BiddingV1
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func NewBidding(model *data.BiddingV1, fea []float32, feaSignMap map[string]data.Feature) *Bidding {
	var fc gl.FastConfig
	cErr := gl.BoosterPredictForMatSingleRowFastInit(model.Model, gl.PredictNormal, 0, -1, gl.Dtype_float32, BidFeaNum, "", &fc)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return nil
	}
	bd := &Bidding{
		fea:        fea,
		fastConfig: fc,
		feaSignMap: feaSignMap,
		Model:      model,
	}
	return bd
}

func (b *Bidding) predictBiddingWinRate(bid float32) float64 {
	bidI := math.Round(float64(bid))
	show, click, ctr := b.getBidFeas(int(bidI))
	b.fea[BidIndex1] = float32(bidI)
	b.fea[BidIndex2] = show
	b.fea[BidIndex3] = click
	b.fea[BidIndex4] = ctr

	var outLen64 int64
	var out float64
	cErr := gl.BoosterPredictForMatSingleRowFast(b.fastConfig, b.fea, &outLen64, &out)
	if cErr != 0 {
		fmt.Println("Error:", cErr)
		return 0
	}
	out = b.Model.IsotonicModel.Predict(out)

	return out
}

func (b *Bidding) sFunc(V, bid float32) float64 {
	return float64(V-bid) * b.predictBiddingWinRate(bid)
}

func (b *Bidding) GoldenSectionSearch(V float32, list ...float32) float32 {
	// 初始化边界
	bMin, bMax := float32(1.0), V
	if len(list) > 1 {
		bMin = list[0]
		bMax = list[1]
	}

	// 计算初始内部点
	x1 := bMax - (bMax-bMin)/GoldenSection
	x2 := bMin + (bMax-bMin)/GoldenSection

	// 开始搜索
	for i := 0; i < GoldenSectionIterNum; i++ {
		sX1 := b.sFunc(V, x1)
		sX2 := b.sFunc(V, x2)
		//fmt.Printf("curr iter: %d, x1: %.4f, x2: %.4f, bMin: %.4f, bMax: %.4f, sX1: %.4f, sX2: %.4f\n",
		//	i, x1, x2, bMin, bMax, sX1, sX2)

		if sX1 > sX2 {
			bMax = x2
		} else {
			bMin = x1
		}

		// 如果区间足够小，提前终止
		if bMax-bMin < GoldenSectionEpsilon {
			break
		}

		x1 = bMax - (bMax-bMin)/GoldenSection
		x2 = bMin + (bMax-bMin)/GoldenSection
	}

	return (bMin + bMax) / 2
}

func (b *Bidding) getBidFeas(bid int) (show float32, click float32, ctr float32) {
	feasign := md5Hash("bid_price::" + strconv.Itoa(bid))
	stat, exists := b.feaSignMap[feasign]
	if !exists {
		show = 0
		click = 0
	} else {
		show = float32(stat.ShowStats)
		click = float32(stat.ClickStats)
	}
	if !exists || stat.ShowStats < 10 {
		ctr = float32((stat.ClickStats + b.Model.Alpha*b.Model.Beta*b.Model.PriorCTR) / (stat.ShowStats + b.Model.Alpha*b.Model.Beta))
	} else {
		ctr = float32(stat.ClickStats / stat.ShowStats)
	}
	return
}

func (b *Bidding) SetFea(fea []float32) {
	b.fea = fea
}
