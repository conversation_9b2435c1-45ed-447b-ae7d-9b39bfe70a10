package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
)

const (
	NameCtcvrExp = "lightgbmctcvrexp"
)

func init() {
	registry.RegisterSearchPredict(NameCtcvrExp, &LightgbmCtcvrExp{})
}

type LightgbmCtcvrExp struct {
	expName string
}

func (l *LightgbmCtcvrExp) Init(config map[string]string) {
	l.expName = models.AB20Exp7Copy
}

func (l *LightgbmCtcvrExp) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict_ctcvr"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputDataExp[l.expName].CtrData.Row), int32(rc.InputDataExp[l.expName].CtrData.Col)
	outval := make([]float64, row)
	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CtrV1.Model, rc.InputDataExp[l.expName].CtrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, outval)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict ctr error, code: %d", cErr)
		return fmt.Errorf("predict ctr error, code: %d", cErr)
	}
	if rc.CtrV1.NegSampleRate > 0.000001 {
		nsr := rc.CtrV1.NegSampleRate
		for i, v := range outval {
			outval[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCtrVal := make([]float64, row)
	for i, v := range outval {
		calcCtrVal[i] = rc.CtrV1.IsotonicModel.Predict(v)
	}

	if rc.PredictDataExp == nil {
		rc.PredictDataExp = make(map[string]*models.OutputMetrics)
	}
	if rc.PredictDataExp[l.expName] == nil {
		rc.PredictDataExp[l.expName] = &models.OutputMetrics{}
	}

	rc.PredictDataExp[l.expName].CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}

	rc.PredictDataExp[l.expName].CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	row, col = int32(rc.InputDataExp[l.expName].CvrData.Row), int32(rc.InputDataExp[l.expName].CvrData.Col)
	cvrArr := make([]float64, row)

	cErr = gl.BoosterPredictForMat(rc.CvrV1.Model, rc.InputDataExp[l.expName].CvrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, cvrArr)
	if cErr != 0 {
		status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict cvr error, code: %d", cErr)
		return fmt.Errorf("predict cvr error, code: %d", cErr)
	}
	if rc.CvrV1.NegSampleRate > 0.000001 {
		nsr := rc.CvrV1.NegSampleRate
		for i, v := range cvrArr {
			cvrArr[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCvrVal := make([]float64, row)
	for i, v := range cvrArr {
		calcCvrVal[i] = rc.CvrV1.IsotonicModel.Predict(v)
	}
	rc.PredictDataExp[l.expName].CvrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCvrVal,
	}

	rc.PredictDataExp[l.expName].CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	return nil
}
