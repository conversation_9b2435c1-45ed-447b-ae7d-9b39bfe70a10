package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"math"
	"strconv"
)

const (
	NameOppo       = "lightgbmoppo"
	NameOppoExp    = "lightgbmoppoexp"
	NameOppoCpi    = "lightgbmoppocpi"
	NameOppoCpiNew = "lightgbmoppocpi2"
)

func init() {
	registry.RegisterSearchPredict(NameOppo, &lightgbmOppo{})
	registry.RegisterSearchPredict(NameOppoExp, &lightgbmOppoExp{})
	registry.RegisterSearchPredict(NameOppoCpi, &lightgbmOppoCpi{})
	registry.RegisterSearchPredict(NameOppoCpiNew, &lightgbmOppoCpiNew{})
}

type lightgbmOppoCpiNew struct {
	lightgbmOppo
}

func (l *lightgbmOppoCpiNew) Init(config map[string]string) {
	l.expName = models.CPIExp
}

type lightgbmOppoCpi struct {
	lightgbmOppo
}

func (l *lightgbmOppoCpi) Init(config map[string]string) {
	l.expName = models.AB20Exp8Copy
}

type lightgbmOppoExp struct {
	lightgbmOppo
}

func (l *lightgbmOppoExp) Init(config map[string]string) {
	l.expName = models.CPAExp
}

type lightgbmOppo struct {
	expName string
}

func (l *lightgbmOppo) Init(config map[string]string) {
	l.expName = models.CPAGroup
}

func (l *lightgbmOppo) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict_bidding"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputDataExp[l.expName].CtrData.Row), int32(rc.InputDataExp[l.expName].CtrData.Col)
	outval := make([]float64, row)

	// 直接将CTR预测值设为1，跳过模型预测
	for i := range outval {
		outval[i] = 1.0
	}

	calcCtrVal := make([]float64, row)
	for i := range outval {
		calcCtrVal[i] = 1.0
	}

	if rc.PredictDataExp == nil {
		rc.PredictDataExp = make(map[string]*models.OutputMetrics)
	}
	if rc.PredictDataExp[l.expName] == nil {
		rc.PredictDataExp[l.expName] = &models.OutputMetrics{}
	}

	rc.PredictDataExp[l.expName].CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}

	rc.PredictDataExp[l.expName].CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	row, col = int32(rc.InputDataExp[l.expName].CvrData.Row), int32(rc.InputDataExp[l.expName].CvrData.Col)
	cvrArr := make([]float64, row)

	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CvrCpa.Model, rc.InputDataExp[l.expName].CvrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, cvrArr)
	if cErr != 0 {
		//status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict cvr error, code: %d", cErr)
		return fmt.Errorf("predict cvr error, code: %d", cErr)
	}
	if rc.CvrCpa.NegSampleRate > 0.000001 {
		nsr := rc.CvrCpa.NegSampleRate
		for i, v := range cvrArr {
			cvrArr[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCvrVal := make([]float64, row)
	for i, v := range cvrArr {
		calcCvrVal[i] = rc.CvrCpa.IsotonicModel.Predict(v)
	}
	rc.PredictDataExp[l.expName].CvrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCvrVal,
	}

	rc.PredictDataExp[l.expName].CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil || rc.InputDataExp[l.expName].BiddingData == nil {
		return fmt.Errorf("bidding data is nil")
	}
	if len(rc.InputDataExp[l.expName].BiddingData.Data) == 0 {
		return fmt.Errorf("bidding data is nil")
	}

	rc.PredictDataExp[l.expName].BiddingData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputDataExp[l.expName].BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputDataExp[l.expName].BiddingData.Data)),
	}
	rc.PredictDataExp[l.expName].PredictBidData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputDataExp[l.expName].BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputDataExp[l.expName].BiddingData.Data)),
	}

	// NewBiddingExp from biddingexp.go
	bidding := NewBiddingExp(rc.BiddingV1, rc.InputDataExp[l.expName].BiddingData.Data[0], rc.BiddingV1.FeatureMap, <-rc.BiddingV1.FcPool)
	defer func() {
		rc.BiddingV1.FcPool <- bidding.fastConfig
	}()

	for i, v := range rc.InputDataExp[l.expName].BiddingData.Data {
		bidding.SetFea(v)
		ctr, cvr := rc.PredictDataExp[l.expName].CtrData.Data[i], rc.PredictDataExp[l.expName].CvrData.Data[i]
		if rc.PredictDataExp[l.expName].CtrCaliData != nil {
			ctr = rc.PredictDataExp[l.expName].CtrCaliData.Data[i]
		}
		if rc.PredictDataExp[l.expName].CvrCaliData != nil {
			cvr = rc.PredictDataExp[l.expName].CvrCaliData.Data[i]
		}
		bidOri := rc.ExpAds[l.expName][i].Cpa * ctr * cvr * 1000
		roundBid := math.Round(bidOri)
		rc.PredictDataExp[l.expName].PredictBidData.Data[i] = bidOri
		baseLine := apollo.ApolloRankerConfig().GetBidOriBaseLine()
		if roundBid <= 1 {
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = roundBid
		} else if l.expName == models.CPIExp && bidOri >= baseLine {
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), float32(apollo.ApolloRankerConfig().GetBidMinEcpm()), float32(bidOri)))
		} else if rc.ExpAds[l.expName][i].ConversionType > 1 && l.expName == models.CPAExp && bidOri >= baseLine {
			//获取adid
			adId, _ := strconv.Atoi(rc.ExpAds[l.expName][i].AdId)
			cpaStart, hasCpa := data.BcInstance().GetCpa(adId)

			// 确定下界：根据条件选择合适的下界值
			var lowerBound float64
			if bidOri > cpaStart && data.BcInstance().GetBidType(adId) != 4 && hasCpa {
				lowerBound = cpaStart
			} else {
				lowerBound = apollo.ApolloRankerConfig().GetBidMinEcpm()
			}

			rc.PredictDataExp[l.expName].BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), float32(lowerBound), float32(bidOri)))
		} else {
			start := max(1, float32(bidOri/2))
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), start, float32(bidOri)))
		}
	}

	return nil
}
