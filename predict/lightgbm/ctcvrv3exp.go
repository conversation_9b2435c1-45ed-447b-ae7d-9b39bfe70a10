package lightgbm

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"math"
)

const (
	NameCtcvr3Exp     = "lightgbmctcvr3exp"
	NameUidExp        = "lightgbmuidexp"
	Name4470NewExp    = "lightgbm4470exp"
	NameAllTag4470Exp = "lightgbmalltag4470exp"
	NameCtr4Cvr2      = "lightgbm_Ctr4Cvr2"
	NameSmooth        = "lightgbm_Smooth"
	NameCPIExp        = "lightgbm_CPIEXP"
)

func init() {
	registry.RegisterSearchPredict(NameCtcvr3Exp, &LightgbmCtcvrV3Exp{})
	registry.RegisterSearchPredict(NameUidExp, &LightgbmUidExp{})
	registry.RegisterSearchPredict(Name4470NewExp, &Lightgbm4470NewExp{})
	registry.RegisterSearchPredict(NameAllTag4470Exp, &LightgbmAllTag4470Exp{})
	registry.RegisterSearchPredict(NameCtr4Cvr2, &lightgbmCtr4Cvr2{})
	registry.RegisterSearchPredict(NameSmooth, &lightgbmSmooth{})
	registry.RegisterSearchPredict(NameCPIExp, &lightgbmCPIExp{})
}

type lightgbmCPIExp struct {
	LightgbmCtcvrV3Exp
}

func (l *lightgbmCPIExp) Init(config map[string]string) {
	l.expName = models.CPIExp
}

type lightgbmSmooth struct {
	LightgbmCtcvrV3Exp
}

func (l *lightgbmSmooth) Init(config map[string]string) {
	l.expName = models.SmoothExp
}

type lightgbmCtr4Cvr2 struct {
	LightgbmCtcvrV3Exp
}

func (l *lightgbmCtr4Cvr2) Init(config map[string]string) {
	l.expName = models.Ctr4Cvr2
}

type LightgbmAllTag4470Exp struct {
	LightgbmCtcvrV3Exp
}

func (l *LightgbmAllTag4470Exp) Init(config map[string]string) {
	l.expName = models.AllTag4470Exp
}

type Lightgbm4470NewExp struct {
	LightgbmCtcvrV3Exp
}

func (l *Lightgbm4470NewExp) Init(config map[string]string) {
	l.expName = models.New4470Exp
}

type LightgbmUidExp struct {
	LightgbmCtcvrV3Exp
}

func (l *LightgbmUidExp) Init(config map[string]string) {
	l.expName = models.UidExp
}

type LightgbmCtcvrV3Exp struct {
	expName string
}

func (l *LightgbmCtcvrV3Exp) Init(config map[string]string) {
	l.expName = models.AB20Exp8Copy
}

func (l *LightgbmCtcvrV3Exp) Predict(rc *models.RankContext) error {

	defer tools.Recover()
	funcName := "LightgbmPredictorPredict_bidding"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil {
		return fmt.Errorf("ctr data is nil")
	}
	row, col := int32(rc.InputDataExp[l.expName].CtrData.Row), int32(rc.InputDataExp[l.expName].CtrData.Col)
	outval := make([]float64, row)
	var outLen64 int64
	var cErr int
	cErr = gl.BoosterPredictForMat(rc.CtrV1.Model, rc.InputDataExp[l.expName].CtrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, outval)
	if cErr != 0 {
		//status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict ctr error, code: %d", cErr)
		return fmt.Errorf("predict ctr error, code: %d", cErr)
	}
	if rc.CtrV1.NegSampleRate > 0.000001 {
		nsr := rc.CtrV1.NegSampleRate
		for i, v := range outval {
			outval[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCtrVal := make([]float64, row)
	for i, v := range outval {
		calcCtrVal[i] = rc.CtrV1.IsotonicModel.Predict(v)
	}

	if rc.PredictDataExp == nil {
		rc.PredictDataExp = make(map[string]*models.OutputMetrics)
	}
	if rc.PredictDataExp[l.expName] == nil {
		rc.PredictDataExp[l.expName] = &models.OutputMetrics{}
	}

	rc.PredictDataExp[l.expName].CtrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCtrVal,
	}

	rc.PredictDataExp[l.expName].CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: outval,
	}

	row, col = int32(rc.InputDataExp[l.expName].CvrData.Row), int32(rc.InputDataExp[l.expName].CvrData.Col)
	cvrArr := make([]float64, row)

	cErr = gl.BoosterPredictForMat(rc.CvrV1.Model, rc.InputDataExp[l.expName].CvrData.Data, gl.Dtype_float32, row, col, gl.Row_major, gl.PredictNormal,
		0, -1, "", &outLen64, cvrArr)
	if cErr != 0 {
		//status = functionlog.FuncStatusFailed
		logger.Instance().Error("predict cvr error, code: %d", cErr)
		return fmt.Errorf("predict cvr error, code: %d", cErr)
	}
	if rc.CvrV1.NegSampleRate > 0.000001 {
		nsr := rc.CvrV1.NegSampleRate
		for i, v := range cvrArr {
			cvrArr[i] = v / (v + (1-v)/nsr)
		}
	}

	calcCvrVal := make([]float64, row)
	for i, v := range cvrArr {
		calcCvrVal[i] = rc.CvrV1.IsotonicModel.Predict(v)
	}
	rc.PredictDataExp[l.expName].CvrCaliData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: calcCvrVal,
	}

	rc.PredictDataExp[l.expName].CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	if rc.InputDataExp == nil || rc.InputDataExp[l.expName] == nil || rc.InputDataExp[l.expName].BiddingData == nil {
		return fmt.Errorf("bidding data is nil")
	}
	if len(rc.InputDataExp[l.expName].BiddingData.Data) == 0 {
		return fmt.Errorf("bidding data is nil")
	}

	rc.PredictDataExp[l.expName].BiddingData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputDataExp[l.expName].BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputDataExp[l.expName].BiddingData.Data)),
	}
	rc.PredictDataExp[l.expName].PredictBidData = &models.FloatMetrics[float64]{
		Row:  len(rc.InputDataExp[l.expName].BiddingData.Data),
		Col:  1,
		Data: make([]float64, len(rc.InputDataExp[l.expName].BiddingData.Data)),
	}

	// NewBiddingExp from biddingexp.go
	bidding := NewBiddingExp(rc.BiddingV1, rc.InputDataExp[l.expName].BiddingData.Data[0], rc.BiddingV1.FeatureMap, <-rc.BiddingV1.FcPool)
	defer func() {
		rc.BiddingV1.FcPool <- bidding.fastConfig
	}()

	for i, v := range rc.InputDataExp[l.expName].BiddingData.Data {
		bidding.SetFea(v)
		ctr, cvr := rc.PredictDataExp[l.expName].CtrData.Data[i], rc.PredictDataExp[l.expName].CvrData.Data[i]
		if rc.PredictDataExp[l.expName].CtrCaliData != nil {
			ctr = rc.PredictDataExp[l.expName].CtrCaliData.Data[i]
		}
		if rc.PredictDataExp[l.expName].CvrCaliData != nil {
			cvr = rc.PredictDataExp[l.expName].CvrCaliData.Data[i]
		}
		bidOri := rc.ExpAds[l.expName][i].Cpa * ctr * cvr * 1000
		roundBid := math.Round(bidOri)
		rc.PredictDataExp[l.expName].PredictBidData.Data[i] = bidOri
		if roundBid <= 1 {
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = roundBid
		} else if l.expName == models.CPIExp && bidOri >= apollo.ApolloRankerConfig().GetBidOriBaseLine() {
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), float32(apollo.ApolloRankerConfig().GetBidMinEcpm()), float32(bidOri)))
		} else {
			start := max(1, float32(bidOri/2))
			rc.PredictDataExp[l.expName].BiddingData.Data[i] = float64(bidding.GoldenSectionSearch(float32(bidOri), start, float32(bidOri)))
		}
	}

	return nil
}
