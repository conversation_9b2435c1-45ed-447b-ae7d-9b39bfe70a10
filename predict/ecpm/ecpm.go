package ecpm

import (
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
)

const (
	Name       = "ecpmpredict"
	FeatureNum = 210
)

func init() {
	registry.RegisterSearchPredict(Name, &EcpmPredictor{})
}

type EcpmPredictor struct {
}

func (e *EcpmPredictor) Init(config map[string]string) {
}

func (e *EcpmPredictor) Predict(rc *models.RankContext) error {

	row := int32(len(rc.Request.AdMaterialInfo))

	// 目前cvr数据是从redis获取的定时数据
	ctrArr := make([]float64, row)
	cvrArr := make([]float64, row)

	for i, mi := range rc.Request.AdMaterialInfo {
		ctrArr[i] = data.ScInstance().Ctr(rc.Request.AdSlotInfo.AdSlotId, mi.AdId)
		cvrArr[i] = data.ScInstance().Cvr(rc.Request.AdSlotInfo.AdSlotId, mi.AdId)
	}

	rc.PredictData.CtrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: ctrArr,
	}

	rc.PredictData.CvrData = &models.FloatMetrics[float64]{
		Row:  int(row),
		Col:  1,
		Data: cvrArr,
	}

	return nil
}
