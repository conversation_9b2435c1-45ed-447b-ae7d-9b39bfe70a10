package tfpredict

import (
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"sync"
)

const (
	Name = "tfpredict"
)

func init() {
	registry.RegisterSearchPredict(Name, &TFPredictor{})
}

type TFPredictor struct {
	modelName string
	version   string
	dir       string
	graphName string
	lock      sync.RWMutex
}

// 获取模型的版本号
//http://api.autopai.cloud.corpautohome.com/api/model/repository/version/latestValid?modelRepositoryName=search_ad_rec_v1

type TfNode struct {
	field string
	index int
}

func (t *TFPredictor) Init(config map[string]string) {

}

func (t *TFPredictor) Predict(rc *models.RankContext) error {
	return nil
}
