[online]
serve_addr=:7731
prometheus_addr=:8012
grpc=:50051

[schedule]
log_maintenance=0 0 2 * * ?

[log]
retention_days=1

[apollo]
apollo_search_env=dev
pro=http://10.128.27.200:8081
dev=http://10.128.27.200:8080
app_id=alg-adranker
cluster=5b26ae03eb4318e8
namespaces=application
cache_dir=./apollodata/


[profile-redis]
addr=r-t4nml18j82m52ogv8l.redis.singapore.rds.aliyuncs.com:6379
passwd=BG#Riky&6k@V
db=0
set_timeout=50
read_timeout=50

[cron-redis]
addr=r-t4nml18j82m52ogv8l.redis.singapore.rds.aliyuncs.com:6379
passwd=BG#Riky&6k@V
db=0
set_timeout=500
read_timeout=500

[pre-rank]
ctr_mode_file=./depend/modelfile/ctr/local_ctr_model.txt

[fine-rank]

[kafka-dump]
addr=server1.kafka.dsp.ymob.ap:9092,server2.kafka.dsp.ymob.ap:9092,server3.kafka.dsp.ymob.ap:9092
topic=predictres

[mysql]
host=*************
user_name=alg
pwd=alg123
db=algorithm

