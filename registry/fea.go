package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/feature"
)

var feas = make(map[string]feature.FeaturesExtraction)

func BuildFeaExtraction(config map[string]string) {
	for _, fea := range feas {
		fea.Init(config)
	}
}

func RegisterFeaExtraction(name string, fea feature.FeaturesExtraction) {
	_, exists := feas[name]
	if exists {
		panic(fmt.Errorf("attempted to register duplicate fea extraction named '%s'", name))
	}
	feas[name] = fea
}

func GetFeaExtraction(name string) feature.FeaturesExtraction {
	return feas[name]
}
