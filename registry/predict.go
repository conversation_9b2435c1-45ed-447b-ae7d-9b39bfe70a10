package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/predict"
)

var predicts = make(map[string]predict.Predict)

func BuildSearchPredict(config map[string]string) {
	for _, predict := range predicts {
		predict.Init(config)
	}
}

func RegisterSearchPredict(name string, predict predict.Predict) {
	_, exists := predicts[name]
	if exists {
		panic(fmt.<PERSON><PERSON><PERSON>("attempted to register duplicate search predict named '%s'", name))
	}
	predicts[name] = predict
}

func GetPredict(name string) predict.Predict {
	return predicts[name]
}
