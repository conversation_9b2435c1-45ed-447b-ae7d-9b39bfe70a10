package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/explore"
)

var explores = make(map[string]explore.TrafficExplore)

func BuildExplore(config map[string]string) {
	for _, explore := range explores {
		explore.Init(config)
	}
}

func RegisterTrafficExplore(name string, explore explore.TrafficExplore) {
	_, exists := explores[name]
	if exists {
		panic(fmt.Errorf("attempted to register duplicate EE named '%s'", name))
	}
	explores[name] = explore
}

func GetTrafficExplore(name string) explore.TrafficExplore {
	return explores[name]
}
