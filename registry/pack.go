package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/pack"
)

var packs = make(map[string]pack.ResultPack)

func BuildResultPack(config map[string]string) {
	for _, p := range packs {
		p.Init(config)
	}
}

func RegisterResultPack(name string, p pack.ResultPack) {
	_, exists := packs[name]
	if exists {
		panic(fmt.Errorf("attempted to register duplicate fea extraction named '%s'", name))
	}
	packs[name] = p
}

func GetResultPack(name string) pack.ResultPack {
	return packs[name]
}
