package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/dump"
)

var dumps = make(map[string]dump.LogDump)

func BuildLogDump(config map[string]string) {
	for _, dump := range dumps {
		dump.Init(config)
	}
}

func RegisterLogDump(name string, dump dump.LogDump) {
	_, exists := dumps[name]
	if exists {
		panic(fmt.Errorf("attempted to register duplicate log dump named '%s'", name))
	}
	dumps[name] = dump
}

func GetLogDump(name string) dump.LogDump {
	return dumps[name]
}
