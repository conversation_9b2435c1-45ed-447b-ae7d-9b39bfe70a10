package registry

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/feature"
)

var pros = make(map[string]feature.FeaturesProcessing)

func BuildFeaturesProcessing(config map[string]string) {
	for _, pro := range pros {
		pro.Init(config)
	}
}

func RegisterFeaturesProcessing(name string, pro feature.FeaturesProcessing) {
	_, exists := pros[name]
	if exists {
		panic(fmt.Errorf("attempted to register duplicate fea processing named '%s'", name))
	}
	pros[name] = pro
}

func GetFeaturesProcessing(name string) feature.FeaturesProcessing {
	return pros[name]
}
