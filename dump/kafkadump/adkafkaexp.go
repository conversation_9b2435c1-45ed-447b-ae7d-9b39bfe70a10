package kafkadump

import (
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	d "gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"math"
	"math/rand"
	"strconv"
	"time"
)

const (
	NameDumpExp = "kafkadumpexp"
)

func init() {
	registry.RegisterLogDump(NameDumpExp, &KafkaDumpExp{})
}

type KafkaDumpExp struct {
}

func (k *KafkaDumpExp) Init(c map[string]string) {
}

// Dump 需要注意，使用无锁的模式，需保证没有并发读写
func (k *KafkaDumpExp) Dump(rc *models.RankContext) {
	defer tools.Recover()
	rand.Seed(time.Now().UnixNano())
	for name, mis := range rc.ExpAds {
		for i, item := range mis {
			if rand.Intn(100) != 0 {
				continue // 1/100 概率采样
			}
			ctrScore, cvrScore := rc.PredictDataExp[name].CtrData.Data[i], rc.PredictDataExp[name].CvrData.Data[i]
			var caliCtr float64
			if rc.PredictDataExp[name].CtrCaliData != nil {
				caliCtr = rc.PredictDataExp[name].CtrCaliData.Data[i]
			}
			var caliCvr float64
			if rc.PredictDataExp[name].CvrCaliData != nil {
				caliCvr = rc.PredictDataExp[name].CvrCaliData.Data[i]
			}
			var install []int32
			if rc.UserFeature != nil && len(rc.UserFeature.InstallList) > 0 {
				install = tools.MergeAndDeduplicate(rc.UserFeature.InstallList, rc.Request.DeviceInfo.InstallList)
			} else {
				install = rc.Request.DeviceInfo.InstallList
			}

			var (
				gender           int32
				recommended      []int32
				searchRecommends []int32
			)

			if rc.UserFeature != nil {
				gender = rc.UserFeature.Gender
				recommended = rc.UserFeature.RecommendedPackageIds
				searchRecommends = rc.UserFeature.SearchRecommendations
			}

			var bidori float64
			var bidmodel float64
			var bidpredict float64
			if rc.PredictDataExp[name].BiddingData != nil {
				bidmodel = math.Round(rc.PredictDataExp[name].BiddingData.Data[i])
			}
			if rc.PredictDataExp[name].PredictBidData != nil {
				bidori = rc.PredictDataExp[name].PredictBidData.Data[i]
			}
			if rc.PredictDataExp[name].BiddingOriData != nil {
				bidpredict = rc.PredictDataExp[name].BiddingOriData.Data[i]
			}
			adFeatureID := item.AdId
			adIdInt, err := strconv.Atoi(item.AdId)
			if err == nil {
				uid := d.BcInstance().GetUid(adIdInt)
				if uid != "" {
					adFeatureID = uid
				}
			}

			// 发送预测日志到Kafka
			predLog := &models.PredictionLog{
				BidId:               item.BidId,
				Timestamp:           time.Now().Unix(),
				AdId:                item.AdId,
				CtrScore:            ctrScore,
				CvrScore:            cvrScore,
				ChannelId:           rc.Request.DeviceInfo.ChannelId,
				Country:             rc.Request.DeviceInfo.CountryCode,
				DeviceModel:         rc.Request.DeviceInfo.Model,
				DeviceType:          rc.Request.DeviceInfo.DeviceType,
				Os:                  rc.Request.DeviceInfo.Os,
				OsVersion:           rc.Request.DeviceInfo.OsVersion,
				NetworkType:         rc.Request.DeviceInfo.NetworkType,
				AdvertiserType:      rc.Request.AdSlotInfo.AdvertiserType,
				AppId:               rc.Request.AdSlotInfo.SourcePackage,
				TagId:               rc.Request.AdSlotInfo.AdSlotId,
				DeviceId:            rc.Request.DeviceInfo.DeviceId,
				DeviceIdType:        rc.Request.DeviceInfo.DeviceIdType,
				DeviceBrand:         rc.Request.DeviceInfo.Brand,
				Ip:                  rc.Request.DeviceInfo.Ip,
				TrafficMaterialSize: rc.Request.AdSlotInfo.Size,
				Gender:              gender,
				InstallList:         install,
				RecommendedPackage:  recommended,
				SearchRecommends:    searchRecommends,
				AbTest:              rc.ExpNames[name][i],
				CreativeGroupId:     item.CreativeGroupId,
				TargetPackage:       item.TargetPackage,
				Timezone:            item.Timezone,
				AccountId:           item.AccountId,
				ProjectId:           item.ProjectId,
				MaterialType:        item.MaterialIdType,
				TitleId:             item.TitleId,
				DescriptionId:       item.DescriptionId,
				DeliveryMethod:      rc.Request.AdSlotInfo.DeliveryType,
				MaterialSize:        item.MaterialSize,
				ImageMaterialId:     item.ImageMaterialId,
				VideoMaterialId:     item.VideoMaterialId,
				Cpa:                 item.Cpa,
				CaliCtr:             caliCtr,
				CaliCvr:             caliCvr,
				BidOri:              bidori,
				BidModel:            bidmodel,
				BidPredic:           bidpredict,
				AdUid:               adFeatureID,
			}
			//logger.Instance().Info("send prediction log to kafka, BidOri: %s ,BidModel : %s ,BidPredic : %s", bidori, bidmodel, bidpredict)
			if apollo.ApolloRankerConfig().DumpKafkaEnable() {
				tools.SendPredictionLog(predLog)
			}
		}
	}
}
