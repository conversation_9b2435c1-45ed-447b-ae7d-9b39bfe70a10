package kafkadump

import (
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"math"
	"math/rand"
	"time"
)

const (
	Name = "kafkadump"
)

func init() {
	registry.RegisterLogDump(Name, &KafkaDump{})
}

type KafkaDump struct {
}

func (k *KafkaDump) Init(c map[string]string) {
}

// Dump 需要注意，使用无锁的模式，需保证没有并发读写
func (k *KafkaDump) Dump(rc *models.RankContext) {
	defer tools.Recover()
	rand.Seed(time.Now().UnixNano())
	for i, material := range rc.Request.AdMaterialInfo {
		if rand.Intn(10) != 0 {
			continue // 1/10 概率采样
		}
		ctrScore := rc.PredictData.CtrData.Data[i]
		cvrScore := rc.PredictData.CvrData.Data[i]
		var caliCtr float64
		if rc.PredictData.CtrCaliData != nil {
			caliCtr = rc.PredictData.CtrCaliData.Data[i]
		}
		var caliCvr float64
		if rc.PredictData.CvrCaliData != nil {
			caliCvr = rc.PredictData.CvrCaliData.Data[i]
		}

		var install []int32
		if rc.UserFeature != nil && len(rc.UserFeature.InstallList) > 0 {
			install = tools.MergeAndDeduplicate(rc.UserFeature.InstallList, rc.Request.DeviceInfo.InstallList)
		} else {
			install = rc.Request.DeviceInfo.InstallList
		}

		var (
			gender           int32
			recommended      []int32
			searchRecommends []int32
		)

		if rc.UserFeature != nil {
			gender = rc.UserFeature.Gender
			recommended = rc.UserFeature.RecommendedPackageIds
			searchRecommends = rc.UserFeature.SearchRecommendations
		}
		var bidori float64
		var bidmodel float64
		if rc.PredictData.BiddingData != nil {
			bidmodel = math.Round(rc.PredictData.BiddingData.Data[i])
		}
		if rc.PredictData.PredictBidData != nil {
			bidori = rc.PredictData.PredictBidData.Data[i]
		}

		// 发送预测日志到Kafka
		predLog := &models.PredictionLog{
			BidId:               material.BidId,
			Timestamp:           time.Now().Unix(),
			AdId:                material.AdId,
			CtrScore:            float64(ctrScore),
			CvrScore:            float64(cvrScore),
			ChannelId:           rc.Request.DeviceInfo.ChannelId,
			Country:             rc.Request.DeviceInfo.CountryCode,
			DeviceModel:         rc.Request.DeviceInfo.Model,
			DeviceType:          rc.Request.DeviceInfo.DeviceType,
			Os:                  rc.Request.DeviceInfo.Os,
			OsVersion:           rc.Request.DeviceInfo.OsVersion,
			NetworkType:         rc.Request.DeviceInfo.NetworkType,
			AdvertiserType:      rc.Request.AdSlotInfo.AdvertiserType,
			AppId:               rc.Request.AdSlotInfo.SourcePackage,
			TagId:               rc.Request.AdSlotInfo.AdSlotId,
			DeviceId:            rc.Request.DeviceInfo.DeviceId,
			DeviceIdType:        rc.Request.DeviceInfo.DeviceIdType,
			DeviceBrand:         rc.Request.DeviceInfo.Brand,
			Ip:                  rc.Request.DeviceInfo.Ip,
			TrafficMaterialSize: rc.Request.AdSlotInfo.Size,
			Gender:              gender,
			InstallList:         install,
			RecommendedPackage:  recommended,
			SearchRecommends:    searchRecommends,
			AbTest:              rc.Request.AbTest,
			CreativeGroupId:     material.CreativeGroupId,
			TargetPackage:       material.TargetPackage,
			Timezone:            material.Timezone,
			AccountId:           material.AccountId,
			ProjectId:           material.ProjectId,
			MaterialType:        material.MaterialIdType,
			TitleId:             material.TitleId,
			DescriptionId:       material.DescriptionId,
			DeliveryMethod:      rc.Request.AdSlotInfo.DeliveryType,
			MaterialSize:        material.MaterialSize,
			ImageMaterialId:     material.ImageMaterialId,
			VideoMaterialId:     material.VideoMaterialId,
			Cpa:                 material.Cpa,
			CaliCtr:             caliCtr,
			CaliCvr:             caliCvr,
			BidOri:              bidori,
			BidModel:            bidmodel,
		}
		if apollo.ApolloRankerConfig().DumpKafkaEnable() {
			tools.SendPredictionLog(predLog)
		}
	}
}
