package apollo

import (
	"encoding/json"
	"strconv"
	"strings"
	"sync"

	"github.com/philchia/agollo/v4"
	"gitlab.ydmob.com/algorithm/brief-framework/config"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

const (
	defaultApolloUrlSearchEngine = "http://127.0.0.1:8080"
	defaultCacheDirSearchEngine  = "./apollodata/"
	defaultAppIDSearchEngine     = "alg-adranker"
	defaultMetaAddrSearchEngine  = "dev"
)

func InitNS() {
	env := config.Instance().MustValue("apollo", "apollo_search_env", defaultMetaAddrSearchEngine)
	prodUrl := config.Instance().MustValue("apollo", env, defaultApolloUrlSearchEngine)
	applicationId := config.Instance().MustValue("apollo", "app_id", defaultAppIDSearchEngine)
	nameSpace := config.Instance().MustValueArray("apollo", "namespaces", ",")
	cacheDir := config.Instance().MustValue("apollo", "cache_dir", defaultCacheDirSearchEngine)

	agollo.Start(&agollo.Conf{
		AppID:          applicationId,
		NameSpaceNames: nameSpace,
		MetaAddr:       prodUrl,
		CacheDir:       cacheDir,
	})

	//初始化，key一定要在converterMap中注册
	InitRankerConfig(nameSpace)

	agollo.OnUpdate(func(event *agollo.ChangeEvent) {
		rankerConfig.lock.Lock()
		for k, v := range event.Changes {
			if c, ok := converterMap[k]; ok {
				err := c.Convert(k, v.NewValue)
				logger.Instance().Warn("apollo convert err=%v", err)
			}
		}
		rankerConfig.lock.Unlock()
		b, _ := json.Marshal(rankerConfig)
		logger.Instance().Info("apollo onUpdate, val = %s", string(b))
	})
}

func ApolloRankerConfig() *RankerConfig {
	return rankerConfig
}

var (
	rankerConfig *RankerConfig
	converterMap = make(map[string]configConverter)
)

func init() {

	converterMap["get_user_feature_timeout"] = new(intConverter)
	converterMap["user_feature_get_enable"] = new(boolConverter)
	converterMap["response_ad_num"] = new(intConverter)
	converterMap["ecpm_multiplier_limit"] = new(floatConverter)
	converterMap["req_log_enable"] = new(boolConverter)
	converterMap["ctr_summary_enable"] = new(boolConverter)
	converterMap["dump_kafka_enable"] = new(boolConverter)
	// 新增配置
	converterMap["bid_adjustment_tag_id_whitelist"] = new(stringSetConverter)
	converterMap["bid_adjustment_factor"] = new(floatConverter)
	converterMap["bid_min_ecpm"] = new(floatConverter)
	converterMap["bid_ori_baseline"] = new(floatConverter)

	rankerConfig = &RankerConfig{
		userFeatureTimeout: 30,
		stringSet:          make(map[string]bool),
	}
}

func InitRankerConfig(nameSpaces []string) {
	rankerConfig.lock.Lock()
	defer rankerConfig.lock.Unlock()
	for _, nameSpace := range nameSpaces {
		for k, c := range converterMap {
			val := agollo.GetString(k, agollo.WithNamespace(nameSpace))
			c.Convert(k, val)
		}
	}
	b, _ := json.Marshal(rankerConfig)
	logger.Instance().Info("init rankerConfig data = %s", string(b))
}

type RankerConfig struct {
	lock                 sync.RWMutex
	userFeatureTimeout   int
	userFeatureGetEnable bool
	responseAdNum        int
	ecpmMultiplierLimit  float64
	reqLogEnable         bool
	ctrSummaryEnable     bool
	dumpKafkaEnable      bool
	stringSet            map[string]bool
	numberConfig         float64
	bidMinEcpm           float64
	bidOriBaseLine       float64
}

func (r *RankerConfig) GetUserFeatureTimeout() int {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.userFeatureTimeout
}

func (r *RankerConfig) GetUserFeatureEnable() bool {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.userFeatureGetEnable
}

func (r *RankerConfig) GetResponseAdNum() int {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.responseAdNum
}

func (r *RankerConfig) GetBidOriBaseLine() float64 {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.bidOriBaseLine
}

func (r *RankerConfig) GetEcpmMultiplierLimit() float64 {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.ecpmMultiplierLimit
}

func (r *RankerConfig) ReqLogEnable() bool {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.reqLogEnable
}

func (r *RankerConfig) CtrSummaryEnable() bool {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.ctrSummaryEnable
}

func (r *RankerConfig) DumpKafkaEnable() bool {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return rankerConfig.dumpKafkaEnable
}

func (r *RankerConfig) GetbidAdjustmentTagIdWhitelist(tagid string) bool {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return r.stringSet[tagid]
}

func (r *RankerConfig) GetBidAdjustmentFactor() float64 {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return r.numberConfig
}

func (r *RankerConfig) GetBidMinEcpm() float64 {
	r.lock.RLock()
	defer r.lock.RUnlock()
	return r.bidMinEcpm
}

type configConverter interface {
	Convert(key, value string) error
}

type boolConverter struct {
}

func (p *boolConverter) Convert(key, value string) error {
	val, err := strconv.ParseBool(value)
	if err != nil {
		return err
	}
	switch key {
	case "user_feature_get_enable":
		rankerConfig.userFeatureGetEnable = val
	case "req_log_enable":
		rankerConfig.reqLogEnable = val
	case "ctr_summary_enable":
		rankerConfig.ctrSummaryEnable = val
	case "dump_kafka_enable":
		rankerConfig.dumpKafkaEnable = val

	}
	return nil
}

type intConverter struct {
}

func (p *intConverter) Convert(key, value string) error {
	val, err := strconv.Atoi(value)
	if err != nil {
		return err
	}
	switch key {
	case "user_feature_timeout":
		rankerConfig.userFeatureTimeout = val
	case "response_ad_num":
		rankerConfig.responseAdNum = val
	}
	return nil
}

type floatConverter struct {
}

func (p *floatConverter) Convert(key, value string) error {
	val, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return err
	}
	switch key {
	case "ecpm_multiplier_limit":
		rankerConfig.ecpmMultiplierLimit = val
	case "bid_adjustment_factor":
		rankerConfig.numberConfig = val
	case "bid_min_ecpm":
		rankerConfig.bidMinEcpm = val
	case "bid_ori_baseline":
		rankerConfig.bidOriBaseLine = val
	}
	return nil
}

type stringSetConverter struct{}

func (p *stringSetConverter) Convert(key, value string) error {
	elements := strings.Split(value, ",")
	newSet := make(map[string]bool)

	for _, element := range elements {
		element = strings.TrimSpace(element)
		if element != "" {
			newSet[element] = true
		}
	}

	switch key {
	case "bid_adjustment_tag_id_whitelist":
		rankerConfig.stringSet = newSet
	}
	return nil
}
