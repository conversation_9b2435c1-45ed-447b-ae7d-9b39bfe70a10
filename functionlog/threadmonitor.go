package functionlog

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/adranker/functionlog/promclient"
	"runtime"
)

func init() {
	cron := cron.New()
	cron.AddFunc("*/10 * * * * ?", func() {
		threads, _ := runtime.ThreadCreateProfile(nil)
		promclient.GaugeValueSet("thread_num", float64(threads))
		promclient.GaugeValueSet("goroutine_num", float64(runtime.NumGoroutine()))
	})
	cron.Start()
}
