package promclient

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	con "gitlab.ydmob.com/algorithm/brief-framework/config"
	"log"
	"math/rand"
	"net/http"
	"time"
)

type metrics struct {
	summaries *prometheus.SummaryVec
	counters  *prometheus.CounterVec
	gauges    *prometheus.GaugeVec
	//histograms prometheus.Histogram
	appname  string
	envname  string
	sampling int // 采样率
}

func SetAppname(appname string) {
	if mt != nil {
		mt.appname = appname
	}
}

func SetEnvname(envname string) {
	if mt != nil {
		mt.envname = envname
	}
}

func NewMetrics(reg prometheus.Registerer) *metrics {
	m := &metrics{
		summaries: prometheus.NewSummaryVec(
			prometheus.SummaryOpts{
				Name:       "durations_ms",
				Help:       "time interval in milliseconds.",
				Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.001, 0.99: 0.001, 0.999: 0.001},
			},
			[]string{"method", "appname", "envname"},
		),
		counters: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "counters",
				Help: "counter",
			},
			[]string{"method", "appname", "envname"},
		),
		gauges: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gauges",
				Help: "gauge",
			},
			[]string{"method", "appname", "envname"},
		),
		sampling: 20, // 改为1/20
	}
	reg.MustRegister(m.summaries)
	reg.MustRegister(m.counters)
	reg.MustRegister(m.gauges)
	rand.Seed(time.Now().UnixNano())
	return m
}

var (
	mt  *metrics
	reg *prometheus.Registry
)

func init() {
	// Create a non-global registry.
	reg = prometheus.NewRegistry()
	// Create new metrics and register them using the custom registry.
	mt = NewMetrics(reg)
	// Add Go module build info.
	reg.MustRegister(collectors.NewBuildInfoCollector())

	go func() {
		http.Handle("/metrics", promhttp.HandlerFor(
			reg,
			promhttp.HandlerOpts{
				// Opt into OpenMetrics to support exemplars.
				EnableOpenMetrics: true,
				// Pass custom registry
				Registry: reg,
			},
		))
		addr := con.Instance().MustValue("online", "prometheus_addr", ":8002")
		log.Fatal(http.ListenAndServe(addr, nil))
	}()
}

type MetricsItem struct {
	Method  string
	Appname string
	Envname string
	Value   float64
}

func SummaryValue(method string, value float64) {
	summaryLabelValue(value, method, mt.appname, mt.envname)
}

func CounterValueAdd(method string, value float64) {
	counterLabelValueAdd(value, method, mt.appname, mt.envname)
}

func GaugeValueSet(method string, value float64) {
	gaugeLabelValueSet(value, method, mt.appname, mt.envname)
}

func SummaryValueItem(mi *MetricsItem) {
	summaryLabelValue(mi.Value, mi.Method, mi.Appname, mi.Envname)
}

func CounterValueAddItem(mi *MetricsItem) {
	counterLabelValueAdd(mi.Value, mi.Method, mi.Appname, mi.Envname)
}

func GaugeValueSetItem(mi *MetricsItem) {
	gaugeLabelValueSet(mi.Value, mi.Method, mi.Appname, mi.Envname)
}

func summaryLabelValue(v float64, lvs ...string) {
	mt.summaries.WithLabelValues(lvs...).Observe(v)
}

func counterLabelValueAdd(v float64, lvs ...string) {
	mt.counters.WithLabelValues(lvs...).Add(v)
}

func gaugeLabelValueSet(v float64, lvs ...string) {
	mt.gauges.WithLabelValues(lvs...).Set(v)
}

type summaryUnit struct {
	start  time.Time
	method string
}

func NewSummaryUnit(m string) *summaryUnit {
	su := &summaryUnit{
		start:  time.Now(),
		method: m,
	}
	return su
}

func (u summaryUnit) Settlement(err error) {
	if rand.Intn(mt.sampling) == 0 {
		summaryLabelValue(float64(time.Now().Sub(u.start).Milliseconds()), u.method, mt.appname, mt.envname)
	}
	RateCounter(u.method, err != nil)
}

func RateCounter(method string, hit bool) {
	counterLabelValueAdd(1, method, mt.appname, mt.envname)
	if hit {
		counterLabelValueAdd(1, method+"_hit", mt.appname, mt.envname)
	}
}
