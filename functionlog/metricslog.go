package functionlog

import (
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/functionlog/promclient"
	"net"
	"os"
)

const (
	FuncStatusOK     = 1
	FuncStatusFailed = 0
)

func getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range addrs {
		// check the address type and if it is not a loopback the display it
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

func init() {
	//初始化
	concurrency := 3
	appName := os.Getenv("CLUSTER_APP_NAME")
	if appName == "" {
		appName = "adranker"
	}
	envName := os.Getenv("ENV_NAME")
	if envName == "" {
		envName = "online"
	}
	podIP := os.Getenv("POD_IP")
	if podIP == "" {
		podIP = getLocalIP()
		if podIP == "" {
			panic("get local ip failed")
		}
	}
	fmt.Printf("metrics.InitWithAppInfo: concurrency=%v, appName=%v, envName=%v, instance=%v\n", concurrency, appName, envName, podIP)
	promclient.SetEnvname(envName)
	promclient.SetAppname(appName)
}

func DeferFunctionMonitorSDK(funcName *string, status *int, message *string) func() {
	su := promclient.NewSummaryUnit(*funcName)
	return func() {
		if funcName == nil || *funcName == "" {
			return
		}
		var err error
		if status != nil && *status == FuncStatusOK { //成功
			err = nil
		} else { //失败
			if message != nil {
				err = fmt.Errorf(*message)
			} else {
				err = fmt.Errorf("")
			}
		}
		su.Settlement(err)
	}
}
