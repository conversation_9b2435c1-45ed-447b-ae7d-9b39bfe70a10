package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctr4470v2 *CtrV1
var lockCtr4470V2 sync.RWMutex
var ctr4470v2cron *cron.Cron

func Ctr4470V2Instance() *CtrV1 {
	lockCtr4470V2.RLock()
	defer lockCtr4470V2.RUnlock()
	return ctr4470v2
}

const Ctr4470V2Name = "oppo-1004470-ctr"
const Ctr4470V2BigVersion = "v2"
const Ctr4470V2LocalFilePath = "./depend/modelfile/ctr4470V2/model.tar.gz"
const Ctr4470V2CaliFilePath = "./depend/modelfile/ctr4470V2/cali_ctr_model_4470.json"
const Ctr4470V2ChecksumFilePath = "./depend/modelfile/ctr4470V2/checksum"
const Ctr4470V2FeatureSignPath = "./depend/modelfile/ctr4470V2/feature_sign.txt"
const Ctr4470V2InstallListPath = "./depend/modelfile/ctr4470V2/install_list.txt"
const Ctr4470V2ModelFilePath = "./depend/modelfile/ctr4470V2/local_ctr_model_4470.txt"

func init() {
	ctr4470v2 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtr4470V2()
	if err != nil {
		panic(err)
	}
	ctr4470v2cron = cron.New()
	err = ctr4470v2cron.AddFunc("0 17,37,57 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtr4470V2()
		if err != nil {
			logger.Instance().Error("ctr4470v2 reload err %v", err)
		}
	})
	ctr4470v2cron.Start()
}

func reloadCtr4470V2() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(Ctr4470V2Name, Ctr4470V2BigVersion)
	if err != nil {
		return err
	}
	if ver == ctr4470v2.Version {
		logger.Instance().Info("no need to reload ctrV2, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, Ctr4470V2LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, Ctr4470V2ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  Ctr4470V2FeatureSignPath,
			installListPath:  Ctr4470V2InstallListPath,
			modelFilePath:    Ctr4470V2ModelFilePath,
			caliFilePath:     Ctr4470V2CaliFilePath,
			checksumFilePath: Ctr4470V2ChecksumFilePath,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtr4470V2.Lock()
	defer lockCtr4470V2.Unlock()
	ctr4470v2 = ctr
	logger.Instance().Info("reload ctr4470V2 success")
	return nil
}
