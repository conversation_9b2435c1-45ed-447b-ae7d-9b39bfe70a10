package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctrv2 *CtrV1
var lockCtrV2 sync.RWMutex
var ctrv2cron *cron.Cron

func CtrV2Instance() *CtrV1 {
	lockCtrV2.RLock()
	defer lockCtrV2.RUnlock()
	return ctrv2
}

//type CtrV2 struct {
//	*LightgbmBase
//}

const CtrV2Name = "all-tagid-ctr"
const CtrV2BigVersion = "v2"
const CtrV2LocalFilePath = "./depend/modelfile/ctrV2/model.tar.gz"
const CtrV2CaliFilePath = "./depend/modelfile/ctrV2/cali_ctr_model.json"
const CtrV2ChecksumFilePath = "./depend/modelfile/ctrV2/checksum"
const CtrV2FeatureSignPath = "./depend/modelfile/ctrV2/feature_sign.txt"
const CtrV2InstallListPath = "./depend/modelfile/ctrV2/install_list.txt"
const CtrV2ModelFilePath = "./depend/modelfile/ctrV2/local_ctr_model.txt"

func init() {
	ctrv2 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtrV2()
	if err != nil {
		panic(err)
	}
	ctrv2cron = cron.New()
	err = ctrv2cron.AddFunc("0 18,38,58 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtrV2()
		if err != nil {
			logger.Instance().Error("ctrv2 reload err %v", err)
		}
	})
	ctrv2cron.Start()
}

func reloadCtrV2() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrV2Name, CtrV2BigVersion)
	if err != nil {
		return err
	}
	if ver == ctrv2.Version {
		logger.Instance().Info("no need to reload ctrV2, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrV2LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrV2ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrV2FeatureSignPath,
			installListPath:  CtrV2InstallListPath,
			modelFilePath:    CtrV2ModelFilePath,
			caliFilePath:     CtrV2CaliFilePath,
			checksumFilePath: CtrV2ChecksumFilePath,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrV2.Lock()
	defer lockCtrV2.Unlock()
	ctrv2 = ctr
	logger.Instance().Info("reload ctrV2 success")
	return nil
}
