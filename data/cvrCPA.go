package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrCPA *CvrV1
var lockCvrCPA sync.RWMutex
var cvrCPAcron *cron.Cron

func CvrCPAInstance() *CvrV1 {
	lockCvrCPA.RLock()
	defer lockCvrCPA.RUnlock()
	return cvrCPA
}

//type CvrV2 struct {
//	*LightgbmBase
//}

const CvrCPAName = "cpa-cvr"
const CvrCPABigVersion = "v2"
const CvrCPALocalFilePath = "./depend/modelfile/cpacvr/model.tar.gz"
const CvrCPAFeatureSignPath = "./depend/modelfile/cpacvr/cpa_feature_sign.txt"
const CvrCPAInstallListPath = "./depend/modelfile/cpacvr/cpa_install_list.txt"
const CvrCPAModelFilePath = "./depend/modelfile/cpacvr/cpa_local_cvr_model.txt"
const CvrCPACaliFilePath = "./depend/modelfile/cpacvr/cpa_cali_cvr_model.json"
const CvrCPAChecksumFilePath = "./depend/modelfile/cpacvr/checksum"

func init() {
	cvrCPA = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCvrCPA()
	if err != nil {
		panic(err)
	}
	cvrCPAcron = cron.New()
	err = cvrCPAcron.AddFunc("0 10,30,50 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCvrCPA()
		if err != nil {
			logger.Instance().Error("cvrCPA reload err %v", err)
		}
	})
	cvrCPAcron.Start()
}

func reloadCvrCPA() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrCPAName, CvrCPABigVersion)
	if err != nil {
		return err
	}
	if ver == cvrCPA.Version {
		logger.Instance().Info("no need to reload cvrCPA, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrCPALocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrCPAChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrCPAFeatureSignPath,
			installListPath:  CvrCPAInstallListPath,
			modelFilePath:    CvrCPAModelFilePath,
			caliFilePath:     CvrCPACaliFilePath,
			checksumFilePath: CvrCPAChecksumFilePath,
			feaList:          feaCatCPA,
			Alpha:            SmoothAlpha,
			Beta:             SmoothBeta,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrCPA.Lock()
	defer lockCvrCPA.Unlock()
	cvrCPA = cvr
	logger.Instance().Info("reload cvrCPA success")
	return nil
}
