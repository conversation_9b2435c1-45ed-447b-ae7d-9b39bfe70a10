package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctrv4 *CtrV1
var lockCtrV4 sync.RWMutex
var ctrv4cron *cron.Cron

func CtrV4Instance() *CtrV1 {
	lockCtrV4.RLock()
	defer lockCtrV4.RUnlock()
	return ctrv4
}

const CtrV4Name = "all-tagid-ctr"
const CtrV4BigVersion = "v4"
const CtrV4LocalFilePath = "./depend/modelfile/ctrV4/model.tar.gz"
const CtrV4CaliFilePath = "./depend/modelfile/ctrV4/cali_ctr_model.json"
const CtrV4ChecksumFilePath = "./depend/modelfile/ctrV4/checksum"
const CtrV4FeatureSignPath = "./depend/modelfile/ctrV4/feature_sign.txt"
const CtrV4InstallListPath = "./depend/modelfile/ctrV4/install_list.txt"
const CtrV4ModelFilePath = "./depend/modelfile/ctrV4/local_ctr_model.txt"

func init() {
	ctrv4 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtrV4()
	if err != nil {
		panic(err)
	}
	ctrv4cron = cron.New()
	err = ctrv4cron.AddFunc("0 07,27,47 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtrV4()
		if err != nil {
			logger.Instance().Error("ctrv4 reload err %v", err)
		}
	})
	ctrv4cron.Start()
}

func reloadCtrV4() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrV4Name, CtrV4BigVersion)
	if err != nil {
		return err
	}
	if ver == ctrv4.Version {
		logger.Instance().Info("no need to reload ctrV4, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrV4LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrV4ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrV4FeatureSignPath,
			installListPath:  CtrV4InstallListPath,
			modelFilePath:    CtrV4ModelFilePath,
			caliFilePath:     CtrV4CaliFilePath,
			checksumFilePath: CtrV4ChecksumFilePath,
			feaList:          feaCatV4,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrV4.Lock()
	defer lockCtrV4.Unlock()
	ctrv4 = ctr
	logger.Instance().Info("reload ctrV4 success")
	return nil
}
