package data

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

// TDO rename : bidRange
type MinBidRangeCollector struct {
	bidRange          map[string]*BidRangeUnit
	bidRangeTimestamp int64
	bidRangeLock      sync.RWMutex
	bidRangeGetter    *redis.Client
	crontab           *cron.Cron
}

var minbidrange *MinBidRangeCollector

func MinBidInstance() *MinBidRangeCollector {
	return minbidrange
}

func init() {
	//minbidrange = &MinBidRangeCollector{
	//	bidRange: make(map[string]*BidRangeUnit),
	//	crontab:  cron.New(),
	//}
	//minbidrange.bidRangeGetter = reader.ProfileRedisInstance()
	//
	//err := minbidrange.UpdateMinBidUnits()
	//if err != nil {
	//	logger.Instance().Error("UpdateRangeUnits err, err = %v", err)
	//	panic(err)
	//}
	//minbidrange.crontab.AddFunc("0 10 */2 * * ?", func() {
	//	// 添加随机
	//	time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
	//	err := minbidrange.UpdateMinBidUnits()
	//	if err != nil {
	//		logger.Instance().Error("UpdateCvr err, err = %v", err)
	//	}
	//})
	//minbidrange.crontab.Start()
}

type BidRangeUnit struct {
	BidVal float64
}

func (s *MinBidRangeCollector) UpdateMinBidUnits() error {
	st := make(map[string]*BidRangeUnit)

	tagids, err := s.bidRangeGetter.SMembers(context.Background(), AllTagIdsKey).Result()
	if err != nil {
		logger.Instance().Error("get bidRange err, err = %v", err)
		return err
	}
	if len(tagids) == 0 {
		return fmt.Errorf("tag_ids is nil")
	}

	//keys := []string{"campaign_bid_tp:oppo-1004470", "campaign_bid_tp:oppo-5001"}
	for _, tag_id := range tagids {
		key := fmt.Sprintf("campaign_bid_tp:%s", tag_id)
		val, err := s.bidRangeGetter.HGetAll(context.Background(), key).Result()

		if err != nil {
			logger.Instance().Error("get bid err, err = %v", err)
			return err
		}

		for k, v := range val {
			bidvalue, err := strconv.ParseFloat(v, 64)
			if err != nil {
				logger.Instance().Error("get bidvalue err, err = %v ,KEY: %s , VALUE: %s", err, key, v)
				continue
			}
			st[tag_id+"_"+k] = &BidRangeUnit{
				BidVal: bidvalue,
			}
		}
	}
	if len(st) == 0 {
		return fmt.Errorf("cvr is nil")
	}
	s.bidRangeLock.Lock()
	defer s.bidRangeLock.Unlock()
	s.bidRange = st
	s.bidRangeTimestamp = time.Now().Unix()
	//logger.Instance().Info("update BidRange success, len(stat) = %d, timestamp = %d", len(st), s.bidRangeTimestamp)
	return nil
}

func (s *MinBidRangeCollector) GetBidRange(tagid, adid string) float64 {
	s.bidRangeLock.RLock()
	defer s.bidRangeLock.RUnlock()
	t := s.bidRange[tagid+"_"+adid]

	//logger.Instance().Info("GetBidRange:%s_%s:%f", tagid, adid, t)

	if t == nil {
		if t := s.bidRange[tagid+"_default"]; t != nil {
			return t.BidVal
		}
		return 0
	}
	return t.BidVal
}
