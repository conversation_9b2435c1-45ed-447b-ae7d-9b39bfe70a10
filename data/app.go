package data

import (
	"database/sql"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"gitlab.ydmob.com/algorithm/brief-framework/config"

	_ "github.com/go-sql-driver/mysql" // 添加 MySQL 驱动
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

type AppCollector struct {
	app          map[string]*AppUnit
	appTimestamp int64
	appLock      sync.RWMutex
	appGetter    *sql.DB
	crontab      *cron.Cron
}

type AppUnit struct {
	PackageName string // 包名
	IndustryIds int    // 行业id
	IabId       string // 行业分类
	IabSubId    string // 行业子分类
}

var app *AppCollector

func AcInstance() *AppCollector {
	return app
}

func init() {
	app = &AppCollector{
		app:     make(map[string]*AppUnit),
		crontab: cron.New(),
	}

	// 初始化 MySQL 连接
	err := app.initMySQLConnection()
	if err != nil {
		logger.Instance().Error("init MySQL connection err, err = %v", err)
		panic(err)
	}

	err = app.UpdateAppUnits()

	if err != nil {
		logger.Instance().Error("UpdateAppUnits err, err = %v", err)
		panic(err)
	}
	app.crontab.AddFunc("0 30 */1 * * ?", func() {
		// 添加随机延迟
		time.Sleep(time.Duration(rand.Int31n(60)) * time.Second)
		err := app.UpdateAppUnits()
		if err != nil {
			logger.Instance().Error("UpdateAppUnits err, err = %v", err)
		}
	})
	app.crontab.Start()
}

// MySQL连接初始化
func (a *AppCollector) initMySQLConnection() error {
	// MySQL 连接配置 - 请根据实际情况修改
	host := config.Instance().MustValue("mysql", "host", "")
	port := 3306
	username := config.Instance().MustValue("mysql", "user_name", "alg")
	password := config.Instance().MustValue("mysql", "pwd", "alg123")
	database := config.Instance().MustValue("mysql", "db", "algorithm")

	// 构建 DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, database)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("open mysql connection failed: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("ping mysql failed: %v", err)
	}
	// 设置连接池参数
	db.SetMaxOpenConns(100)          // 最大打开连接数
	db.SetMaxIdleConns(10)           // 最大空闲连接数
	db.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	a.appGetter = db
	logger.Instance().Info("MySQL connection initialized successfully")
	return nil
}

func (a *AppCollector) UpdateAppUnits() error {
	if a.appGetter == nil {
		return fmt.Errorf("app getter is nil")
	}

	au := make(map[string]*AppUnit)

	query := "SELECT package_name, industry_ids, iab_id, iab_sub_id FROM app"
	rows, err := a.appGetter.Query(query)
	if err != nil {
		logger.Instance().Error("query app err, err = %v", err)
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var packageName, iabId, iabSubId sql.NullString
		var industryIds sql.NullInt32

		// 按照SQL查询的字段顺序进行扫描
		err := rows.Scan(&packageName, &industryIds, &iabId, &iabSubId)
		if err != nil {
			logger.Instance().Error("scan app row err, err = %v", err)
			continue
		}

		// 使用package_name作为key存储数据
		pkgName := getStringValue(packageName)
		if pkgName != "" {
			au[pkgName] = &AppUnit{
				PackageName: pkgName,
				IndustryIds: getIntValue(industryIds),
				IabId:       getStringValue(iabId),
				IabSubId:    getStringValue(iabSubId),
			}
		}
	}

	if err = rows.Err(); err != nil {
		logger.Instance().Error("rows iteration err, err = %v", err)
		return err
	}

	if len(au) == 0 {
		return fmt.Errorf("app data is empty")
	}

	a.appLock.Lock()
	defer a.appLock.Unlock()
	a.app = au
	a.appTimestamp = time.Now().Unix()
	logger.Instance().Info("update app success, len(app) = %d, timestamp = %d", len(au), a.appTimestamp)
	return nil
}

func (a *AppCollector) GetApp(packageName string) *AppUnit {
	a.appLock.RLock()
	defer a.appLock.RUnlock()
	return a.app[packageName]
}

func (a *AppCollector) GetIndustryIds(packageName string) int {
	a.appLock.RLock()
	defer a.appLock.RUnlock()
	if unit := a.app[packageName]; unit != nil {
		return unit.IndustryIds
	}
	return 0
}

func (a *AppCollector) GetIabId(packageName string) string {
	a.appLock.RLock()
	defer a.appLock.RUnlock()
	if unit := a.app[packageName]; unit != nil {
		return unit.IabId
	}
	return ""
}

func (a *AppCollector) GetIabSubId(packageName string) string {
	a.appLock.RLock()
	defer a.appLock.RUnlock()
	if unit := a.app[packageName]; unit != nil {
		return unit.IabSubId
	}
	return ""
}

func (a *AppCollector) GetAllApps() map[string]*AppUnit {
	a.appLock.RLock()
	defer a.appLock.RUnlock()
	// 返回副本以避免并发修改
	result := make(map[string]*AppUnit)
	for k, v := range a.app {
		result[k] = &AppUnit{
			PackageName: v.PackageName,
			IndustryIds: v.IndustryIds,
			IabId:       v.IabId,
			IabSubId:    v.IabSubId,
		}
	}
	return result
}
