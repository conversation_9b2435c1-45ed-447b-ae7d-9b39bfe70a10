package data

import (
	"database/sql"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"gitlab.ydmob.com/algorithm/brief-framework/config"

	_ "github.com/go-sql-driver/mysql" // 添加 MySQL 驱动
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

type BudgetCollector struct {
	budget          map[int]*BudgetUnit
	budgetTimestamp int64
	budgetLock      sync.RWMutex
	budgetGetter    *sql.DB
	crontab         *cron.Cron
}

type BudgetUnit struct {
	Country         string  // 国家
	Channel         string  // 渠道
	AppId           string  // 应用ID
	TotalBudget     float64 // 总预算
	DayBudget       float64 // 日预算
	BidType         int     // 竞价类型
	Cpa             float64 // 目标CPA (对应goal字段)
	CreativeType    string  // 创意类型
	ConversionType  int     // 转化类型
	ConversionPrice float64 // 转化价格
	AlgAdId         string  // 算法广告ID
	Uid             string  // 摘要ID
}

var budget *BudgetCollector

func BcInstance() *BudgetCollector {
	return budget
}

func init() {
	budget = &BudgetCollector{
		budget:  make(map[int]*BudgetUnit),
		crontab: cron.New(),
	}

	// 初始化 MySQL 连接
	err := budget.initMySQLConnection()
	if err != nil {
		logger.Instance().Error("init MySQL connection err, err = %v", err)
		panic(err)
	}

	err = budget.UpdateBudgetUnits()

	// logs中打印Map中所有数据
	/*logger.Instance().Info("GetAllBudgets:")
	for id, unit := range budget.GetAllBudgets() {
		logger.Instance().Info("Uid: %s, CPA: %.2f, ConversionPrice: %.2f, BidType: %d",
			id, unit.Cpa, unit.ConversionPrice, unit.BidType)
	}*/

	if err != nil {
		logger.Instance().Error("UpdateBudgetUnits err, err = %v", err)
		panic(err)
	}
	budget.crontab.AddFunc("0 30 */1 * * ?", func() {
		// 添加随机延迟
		time.Sleep(time.Duration(rand.Int31n(60)) * time.Second)
		err := budget.UpdateBudgetUnits()
		if err != nil {
			logger.Instance().Error("UpdateBudgetUnits err, err = %v", err)
		}
	})
	budget.crontab.Start()
}

// MySQL连接初始化
func (b *BudgetCollector) initMySQLConnection() error {
	// MySQL 连接配置 - 请根据实际情况修改
	host := config.Instance().MustValue("mysql", "host", "")
	port := 3306
	username := config.Instance().MustValue("mysql", "user_name", "alg")
	password := config.Instance().MustValue("mysql", "pwd", "alg123")
	database := config.Instance().MustValue("mysql", "db", "algorithm")

	// 构建 DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, database)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("open mysql connection failed: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("ping mysql failed: %v", err)
	}
	// 设置连接池参数
	db.SetMaxOpenConns(100)          // 最大打开连接数
	db.SetMaxIdleConns(10)           // 最大空闲连接数
	db.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	b.budgetGetter = db
	logger.Instance().Info("MySQL connection initialized successfully")
	return nil
}

func (b *BudgetCollector) UpdateBudgetUnits() error {
	if b.budgetGetter == nil {
		return fmt.Errorf("budget getter is nil")
	}

	bu := make(map[int]*BudgetUnit)

	query := "SELECT id, country,channel,app_id,total_budget,day_budget, bid_type ,goal,creative_type,conversion_type,conversion_price, alg_adid, uid FROM campaign WHERE dt = (SELECT max(dt) FROM campaign)"
	rows, err := b.budgetGetter.Query(query)
	if err != nil {
		logger.Instance().Error("query budget err, err = %v", err)
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var country, channel, creativeType, appId, algAdId, uid sql.NullString
		var totalBudget, dayBudget, cpa, conversionPrice sql.NullFloat64
		var bidType, conversionType sql.NullInt32

		// 按照SQL查询的字段顺序进行扫描
		err := rows.Scan(&id, &country, &channel, &appId, &totalBudget, &dayBudget,
			&bidType, &cpa, &creativeType, &conversionType, &conversionPrice, &algAdId, &uid)
		if err != nil {
			logger.Instance().Error("scan budget row err, err = %v", err)
			continue
		}

		// 修改：使用id作为key存储数据
		bu[id] = &BudgetUnit{
			Country:         getStringValue(country),
			Channel:         getStringValue(channel),
			AppId:           getStringValue(appId),
			TotalBudget:     getFloat64Value(totalBudget),
			DayBudget:       getFloat64Value(dayBudget),
			BidType:         getIntValue(bidType),
			Cpa:             getFloat64Value(cpa) * 100,
			CreativeType:    getStringValue(creativeType),
			ConversionType:  getIntValue(conversionType),
			ConversionPrice: getFloat64Value(conversionPrice) * 100,
			AlgAdId:         getStringValue(algAdId),
			Uid:             getStringValue(uid),
		}
	}

	if err = rows.Err(); err != nil {
		logger.Instance().Error("rows iteration err, err = %v", err)
		return err
	}

	if len(bu) == 0 {
		return fmt.Errorf("budget data is empty")
	}

	b.budgetLock.Lock()
	defer b.budgetLock.Unlock()
	b.budget = bu
	b.budgetTimestamp = time.Now().Unix()
	logger.Instance().Info("update budget success, len(budget) = %d, timestamp = %d", len(bu), b.budgetTimestamp)
	return nil
}

// 辅助函数：处理可能为空的字符串值
func getStringValue(ns sql.NullString) string {
	if ns.Valid {
		return ns.String
	}
	return ""
}

// 辅助函数：处理可能为空的float64值
func getFloat64Value(nf sql.NullFloat64) float64 {
	if nf.Valid {
		return nf.Float64
	}
	return 0.0
}

// 辅助函数：处理可能为空的int值
func getIntValue(ni sql.NullInt32) int {
	if ni.Valid {
		return int(ni.Int32)
	}
	return 0
}

func (b *BudgetCollector) GetBudget(id int) *BudgetUnit {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	return b.budget[id]
}

func (b *BudgetCollector) GetCpa(id int) (float64, bool) {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	if unit := b.budget[id]; unit != nil {
		return unit.Cpa, true
	}
	return 0, false
}

func (b *BudgetCollector) GetConPrice(id int) float64 {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	if unit := b.budget[id]; unit != nil {
		return unit.ConversionPrice
	}
	return 0
}

func (b *BudgetCollector) GetBidType(id int) int {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	if unit := b.budget[id]; unit != nil {
		return unit.BidType
	}
	return 0
}

func (b *BudgetCollector) GetUid(id int) string {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	if unit := b.budget[id]; unit != nil {
		return unit.Uid
	}
	return ""
}

func (b *BudgetCollector) GetAllBudgets() map[int]*BudgetUnit {
	b.budgetLock.RLock()
	defer b.budgetLock.RUnlock()
	// 返回副本以避免并发修改
	result := make(map[int]*BudgetUnit)
	for k, v := range b.budget {
		result[k] = &BudgetUnit{
			Country:         v.Country,
			Channel:         v.Channel,
			AppId:           v.AppId,
			TotalBudget:     v.TotalBudget,
			DayBudget:       v.DayBudget,
			BidType:         v.BidType,
			Cpa:             v.Cpa,
			CreativeType:    v.CreativeType,
			ConversionType:  v.ConversionType,
			ConversionPrice: v.ConversionPrice,
			AlgAdId:         v.AlgAdId,
			Uid:             v.Uid,
		}
	}
	return result
}
