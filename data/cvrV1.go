package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrv1 *CvrV1
var lockCvrV1 sync.RWMutex
var cvrv1cron *cron.Cron

func CvrV1Instance() *CvrV1 {
	lockCvrV1.RLock()
	defer lockCvrV1.RUnlock()
	return cvrv1
}

type CvrV1 struct {
	*LightgbmBase
}

const CvrV1Name = "all-tagid-cvr"
const CvrV1BigVersion = "v1"
const CvrV1LocalFilePath = "./depend/modelfile/cvr/model.tar.gz"
const CvrFeatureSignPath = "./depend/modelfile/cvr/feature_sign.txt"
const CvrInstallListPath = "./depend/modelfile/cvr/install_list.txt"
const CvrModelFilePath = "./depend/modelfile/cvr/local_cvr_model.txt"
const CvrV1CaliFilePath = "./depend/modelfile/cvr/cali_cvr_model.json"
const CvrV1ChecksumFilePath = "./depend/modelfile/cvr/checksum"

func init() {
	cvrv1 = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCvrV1()
	if err != nil {
		panic(err)
	}
	cvrv1cron = cron.New()
	err = cvrv1cron.AddFunc("0 19,39,59 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCvrV1()
		if err != nil {
			logger.Instance().Error("cvrv1 reload err %v", err)
		}
	})
	cvrv1cron.Start()
}

func reloadCvrV1() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrV1Name, CvrV1BigVersion)
	if err != nil {
		return err
	}
	if ver == cvrv1.Version {
		logger.Instance().Info("no need to reload cvrV1, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrV1LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrV1ChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrFeatureSignPath,
			installListPath:  CvrInstallListPath,
			modelFilePath:    CvrModelFilePath,
			caliFilePath:     CvrV1CaliFilePath,
			checksumFilePath: CvrV1ChecksumFilePath,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrV1.Lock()
	defer lockCvrV1.Unlock()
	cvrv1 = cvr
	logger.Instance().Info("reload cvrV1 success")
	return nil
}
