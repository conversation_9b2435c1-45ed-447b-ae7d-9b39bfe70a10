package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrv3 *CvrV1
var lockCvrV3 sync.RWMutex
var cvrv3cron *cron.Cron

func CvrV3Instance() *CvrV1 {
	lockCvrV3.RLock()
	defer lockCvrV3.RUnlock()
	return cvrv3
}

//type CvrV3 struct {
//	*LightgbmBase
//}

const CvrV3Name = "all-tagid-cvr"
const CvrV3BigVersion = "v3"
const CvrV3LocalFilePath = "./depend/modelfile/cvrV3/model.tar.gz"
const CvrV3FeatureSignPath = "./depend/modelfile/cvrV3/feature_sign.txt"
const CvrV3InstallListPath = "./depend/modelfile/cvrV3/install_list.txt"
const CvrV3ModelFilePath = "./depend/modelfile/cvrV3/local_cvr_model.txt"
const CvrV3CaliFilePath = "./depend/modelfile/cvrV3/cali_cvr_model.json"
const CvrV3ChecksumFilePath = "./depend/modelfile/cvrV3/checksum"

func init() {
	cvrv3 = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCvrV3()
	if err != nil {
		panic(err)
	}
	cvrv3cron = cron.New()
	err = cvrv3cron.AddFunc("0 11,31,51 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCvrV3()
		if err != nil {
			logger.Instance().Error("cvrv3 reload err %v", err)
		}
	})
	cvrv3cron.Start()
}

func reloadCvrV3() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrV3Name, CvrV3BigVersion)
	if err != nil {
		return err
	}
	if ver == cvrv3.Version {
		logger.Instance().Info("no need to reload cvrV3, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrV3LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrV3ChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrV3FeatureSignPath,
			installListPath:  CvrV3InstallListPath,
			modelFilePath:    CvrV3ModelFilePath,
			caliFilePath:     CvrV3CaliFilePath,
			checksumFilePath: CvrV3ChecksumFilePath,
			feaList:          feaCatV4,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrV3.Lock()
	defer lockCvrV3.Unlock()
	cvrv3 = cvr
	logger.Instance().Info("reload cvrV3 success")
	return nil
}
