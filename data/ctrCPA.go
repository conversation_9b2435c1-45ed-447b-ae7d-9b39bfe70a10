package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctrCPA *CtrV1
var lockCtrCPA sync.RWMutex
var ctrCPAcron *cron.Cron

func CtrCPAInstance() *CtrV1 {
	lockCtrCPA.RLock()
	defer lockCtrCPA.RUnlock()
	return ctrCPA
}

const CtrCPAName = "cpa-ctr"
const CtrCPABigVersion = "v2"
const CtrCPALocalFilePath = "./depend/modelfile/cpactr/model.tar.gz"
const CtrCPACaliFilePath = "./depend/modelfile/cpactr/cpa_cali_ctr_model.json"
const CtrCPAChecksumFilePath = "./depend/modelfile/cpactr/checksum"
const CtrCPAFeatureSignPath = "./depend/modelfile/cpactr/cpa_feature_sign.txt"
const CtrCPAInstallListPath = "./depend/modelfile/cpactr/cpa_install_list.txt"
const CtrCPAModelFilePath = "./depend/modelfile/cpactr/cpa_local_ctr_model.txt"

func init() {
	ctrCPA = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtrCPA()
	if err != nil {
		panic(err)
	}
	ctrCPAcron = cron.New()
	err = ctrCPAcron.AddFunc("0 12,32,52 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtrCPA()
		if err != nil {
			logger.Instance().Error("ctrCPA reload err %v", err)
		}
	})
	ctrCPAcron.Start()
}

func reloadCtrCPA() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrCPAName, CtrCPABigVersion)
	if err != nil {
		return err
	}
	if ver == ctrCPA.Version {
		logger.Instance().Info("no need to reload ctrCPA, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrCPALocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrCPAChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrCPAFeatureSignPath,
			installListPath:  CtrCPAInstallListPath,
			modelFilePath:    CtrCPAModelFilePath,
			caliFilePath:     CtrCPACaliFilePath,
			checksumFilePath: CtrCPAChecksumFilePath,
			feaList:          feaCatCPA,
			Alpha:            SmoothAlpha,
			Beta:             SmoothBeta,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrCPA.Lock()
	defer lockCtrCPA.Unlock()
	ctrCPA = ctr
	logger.Instance().Info("reload ctrCPA success")
	return nil
}
