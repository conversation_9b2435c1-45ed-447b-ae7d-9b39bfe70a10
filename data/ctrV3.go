package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctrv3 *CtrV1
var lockCtrV3 sync.RWMutex
var ctrv3cron *cron.Cron

func CtrV3Instance() *CtrV1 {
	lockCtrV3.RLock()
	defer lockCtrV3.RUnlock()
	return ctrv3
}

// TODO 模型路径确定
const CtrV3Name = "all-tagid-ctr"
const CtrV3BigVersion = "v3"
const CtrV3LocalFilePath = "./depend/modelfile/ctrV3/model.tar.gz"
const CtrV3CaliFilePath = "./depend/modelfile/ctrV3/cali_ctr_model.json"
const CtrV3ChecksumFilePath = "./depend/modelfile/ctrV3/checksum"
const CtrV3FeatureSignPath = "./depend/modelfile/ctrV3/feature_sign.txt"
const CtrV3InstallListPath = "./depend/modelfile/ctrV3/install_list.txt"
const CtrV3ModelFilePath = "./depend/modelfile/ctrV3/local_ctr_model.txt"

func init() {
	ctrv3 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtrV3()
	if err != nil {
		panic(err)
	}
	ctrv3cron = cron.New()
	err = ctrv3cron.AddFunc("0 12,32,52 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtrV3()
		if err != nil {
			logger.Instance().Error("ctrv3 reload err %v", err)
		}
	})
	ctrv3cron.Start()
}

func reloadCtrV3() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrV3Name, CtrV3BigVersion)
	if err != nil {
		return err
	}
	if ver == ctrv3.Version {
		logger.Instance().Info("no need to reload ctrV3, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrV3LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrV3ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrV3FeatureSignPath,
			installListPath:  CtrV3InstallListPath,
			modelFilePath:    CtrV3ModelFilePath,
			caliFilePath:     CtrV3CaliFilePath,
			checksumFilePath: CtrV3ChecksumFilePath,
			feaList:          feaCatV3,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrV3.Lock()
	defer lockCtrV3.Unlock()
	ctrv3 = ctr
	logger.Instance().Info("reload ctrV3 success")
	return nil
}
