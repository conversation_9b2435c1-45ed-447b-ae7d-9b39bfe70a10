package data

type Isotonic struct {
	X []float64 `json:"X"`
	Y []float64 `json:"y"`
}

// 线性插值预测函数
func (m *Isotonic) Predict(x float64) float64 {
	if x <= m.X[0] {
		return m.Y[0]
	}
	if x >= m.X[len(m.X)-1] {
		return m.Y[len(m.Y)-1]
	}

	for i := 0; i < len(m.X)-1; i++ {
		if x >= m.X[i] && x <= m.X[i+1] {
			x0, x1 := m.X[i], m.X[i+1]
			y0, y1 := m.Y[i], m.Y[i+1]
			t := (x - x0) / (x1 - x0)
			return y0 + t*(y1-y0)
		}
	}
	return 0
}
