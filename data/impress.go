package data

import (
	"context"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/adranker/reader"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

type ImpressCollector struct {
	impress          map[int]int
	impressTimestamp int64
	impressLock      sync.RWMutex
	impressGetter    *redis.Client
	crontab          *cron.Cron
}

var impress *ImpressCollector

func IcInstance() *ImpressCollector {
	return impress
}

func init() {
	impress = &ImpressCollector{
		impress: make(map[int]int),
		crontab: cron.New(),
	}
	impress.impressGetter = reader.CronRedisInstance()

	err := impress.UpdateImpressUnits()

	// crontab中打印Map中所有数据
	/*logger.Instance().Info("GetAllImpress:")
	logger.Instance().Info(impress.GetAllImpress())*/

	if err != nil {
		logger.Instance().Error("UpdateImpressUnits err, err = %v", err)
		panic(err)
	}
	impress.crontab.AddFunc("0 20 */1 * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := impress.UpdateImpressUnits()
		if err != nil {
			logger.Instance().Error("UpdateImpressUnits err, err = %v", err)
		}
	})
	impress.crontab.Start()
}

const CampaignImpressKey = "campaign_impress"

func (i *ImpressCollector) UpdateImpressUnits() error {
	im := make(map[int]int)
	var ts = time.Now().Unix()

	val, err := i.impressGetter.HGetAll(context.Background(), CampaignImpressKey).Result()
	if err != nil {
		logger.Instance().Error("get impress err, err = %v", err)
		return err
	}

	for k, v := range val {
		key, err := strconv.Atoi(k)
		if err != nil {
			logger.Instance().Error("parse key err, key = %s, err = %v", k, err)
			continue
		}
		value, err := strconv.Atoi(v)
		if err != nil {
			logger.Instance().Error("parse value err, value = %s, err = %v", v, err)
			continue
		}
		im[key] = value
	}

	i.impressLock.Lock()
	defer i.impressLock.Unlock()
	i.impress = im
	i.impressTimestamp = ts
	logger.Instance().Info("update impress success, len(impress) = %d, timestamp = %d", len(im), i.impressTimestamp)
	return nil
}

func (i *ImpressCollector) GetImpress(key int) int {
	i.impressLock.RLock()
	defer i.impressLock.RUnlock()
	if value, exists := i.impress[key]; exists {
		return value
	}
	return 0
}

func (i *ImpressCollector) GetAllImpress() map[int]int {
	i.impressLock.RLock()
	defer i.impressLock.RUnlock()
	result := make(map[int]int)
	for k, v := range i.impress {
		result[k] = v
	}
	return result
}
