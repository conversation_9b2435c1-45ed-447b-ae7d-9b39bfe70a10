package data

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/adranker/reader"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"
)

type StatCollector struct {
	stat          map[string]*StatUnit
	statTimestamp int64
	statLock      sync.RWMutex
	statGetter    *redis.Client
	crontab       *cron.Cron
	ecpmDefault   *StatUnit
}

// 为实验保存 oppo-1004470 数据
var stat *StatCollector

func ScInstance() *StatCollector {
	return stat
}

func init() {
	stat = &StatCollector{
		stat:        make(map[string]*StatUnit),
		crontab:     cron.New(),
		ecpmDefault: &StatUnit{},
	}
	stat.statGetter = reader.CronRedisInstance()

	err := stat.UpdateStatUnits()
	if err != nil {
		logger.Instance().Error("UpdateStatUnits err, err = %v", err)
		panic(err)
	}
	stat.crontab.AddFunc("0 10 */1 * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := stat.UpdateStatUnits()
		if err != nil {
			logger.Instance().Error("UpdateCvr err, err = %v", err)
		}
	})
	stat.crontab.Start()
}

type StatUnit struct {
	Ecpm float64
	Ectr float64
	Ecvr float64
}

const AllTagIdsKey = "campaign_metrics:all_tag_ids"
const DefaultEcpmKey = "campaign_metrics:default"

func (s *StatCollector) UpdateStatUnits() error {
	st := make(map[string]*StatUnit)
	var ts int64

	tagids, err := s.statGetter.SMembers(context.Background(), AllTagIdsKey).Result()
	if err != nil {
		logger.Instance().Error("get stat err, err = %v", err)
		return err
	}
	if len(tagids) == 0 {
		return fmt.Errorf("tag_ids is nil")
	}

	//keys := []string{"campaign_metrics:oppo-1004470", "campaign_metrics:oppo-5001"}
	for _, tag_id := range tagids {
		key := fmt.Sprintf("campaign_metrics:%s", tag_id)
		val, err := s.statGetter.HGetAll(context.Background(), key).Result()
		if err != nil {
			logger.Instance().Error("get stat err, err = %v", err)
			return err
		}
		for k, v := range val {
			su, t, err := convertUnit(v)
			if err != nil {
				continue
			}
			st[tag_id+"_"+k] = su
			ts = t
		}
	}
	if len(st) == 0 {
		return fmt.Errorf("cvr is nil")
	}
	ecmpd, err := s.statGetter.Get(context.Background(), DefaultEcpmKey).Result()
	if err != nil {
		logger.Instance().Error("get ecmpd err, err = %v", err)
		return err
	}
	su, _, err := convertUnit(ecmpd)
	if err != nil || su == nil {
		logger.Instance().Error("get ecmpd err, err = %v", err)
		return err
	}

	s.statLock.Lock()
	defer s.statLock.Unlock()
	s.stat = st
	s.statTimestamp = ts
	s.ecpmDefault = su
	logger.Instance().Info("update stat success, len(stat) = %d, timestamp = %d", len(st), s.statTimestamp)
	return nil
}

func (s *StatCollector) Cvr(tagid, adid string) float64 {
	s.statLock.RLock()
	defer s.statLock.RUnlock()
	t := s.stat[tagid+"_"+adid]
	if t == nil {
		return 0
	}
	return t.Ecvr
}
func (s *StatCollector) Ecpm(tagid, adid string) float64 {
	s.statLock.RLock()
	defer s.statLock.RUnlock()
	t := s.stat[tagid+"_"+adid]
	if t == nil {
		t = s.stat[tagid+"_default"]
		if t == nil {
			t = s.ecpmDefault
		}
	}
	return t.Ecpm
}

func (s *StatCollector) Ctr(tagid, adid string) float64 {
	s.statLock.RLock()
	defer s.statLock.RUnlock()
	t := s.stat[tagid+"_"+adid]
	if t == nil {
		return 0
	}
	return t.Ectr
}

func convertUnit(s string) (*StatUnit, int64, error) {
	arr := strings.Split(s, "|")
	if len(arr) != 4 {
		logger.Instance().Error("wrong format, data = %s", s)
		return nil, 0, fmt.Errorf("wrong format, data = %s", s)
	}
	ecmp, err := strconv.ParseFloat(arr[0], 64)
	if err != nil {
		logger.Instance().Error("parse ecmp err, err = %v", err)
		return nil, 0, err
	}
	ectr, err := strconv.ParseFloat(arr[1], 64)
	if err != nil {
		logger.Instance().Error("parse ectr err, err = %v", err)
		return nil, 0, err
	}
	if ectr > 1 {
		ectr = 1
	}
	ecvr, err := strconv.ParseFloat(arr[2], 64)
	if err != nil {
		logger.Instance().Error("parse ecvr err, err = %v", err)
		return nil, 0, err
	}
	if ecvr > 1 {
		ecvr = 1
	}
	t, _ := strconv.ParseInt(arr[3], 10, 64)
	return &StatUnit{
		Ecpm: ecmp,
		Ectr: ectr,
		Ecvr: ecvr,
	}, t, nil
}
