package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrv4 *CvrV1
var lockCvrV4 sync.RWMutex
var cvrv4cron *cron.Cron

func CvrV4Instance() *CvrV1 {
	lockCvrV4.RLock()
	defer lockCvrV4.RUnlock()
	return cvrv4
}

//type Cvrv4 struct {
//	*LightgbmBase
//}

const CvrV4Name = "all-tagid-cvr"
const CvrV4BigVersion = "v4"
const CvrV4LocalFilePath = "./depend/modelfile/cvrV4/model.tar.gz"
const CvrV4FeatureSignPath = "./depend/modelfile/cvrV4/feature_sign.txt"
const CvrV4InstallListPath = "./depend/modelfile/cvrV4/install_list.txt"
const CvrV4ModelFilePath = "./depend/modelfile/cvrV4/local_cvr_model.txt"
const CvrV4CaliFilePath = "./depend/modelfile/cvrV4/cali_cvr_model.json"
const CvrV4ChecksumFilePath = "./depend/modelfile/cvrV4/checksum"

func init() {
	cvrv4 = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCvrV4()
	if err != nil {
		panic(err)
	}
	cvrv4cron = cron.New()
	err = cvrv4cron.AddFunc("0 11,31,51 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCvrV4()
		if err != nil {
			logger.Instance().Error("cvrv4 reload err %v", err)
		}
	})
	cvrv4cron.Start()
}

func reloadCvrV4() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrV4Name, CvrV4BigVersion)
	if err != nil {
		return err
	}
	if ver == cvrv4.Version {
		logger.Instance().Info("no need to reload cvrV4, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrV4LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrV4ChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrV4FeatureSignPath,
			installListPath:  CvrV4InstallListPath,
			modelFilePath:    CvrV4ModelFilePath,
			caliFilePath:     CvrV4CaliFilePath,
			checksumFilePath: CvrV4ChecksumFilePath,
			feaList:          feaCatV3,
			Alpha:            SmoothAlpha,
			Beta:             SmoothBeta,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrV4.Lock()
	defer lockCvrV4.Unlock()
	cvrv4 = cvr
	logger.Instance().Info("reload cvrV4 success")
	return nil
}
