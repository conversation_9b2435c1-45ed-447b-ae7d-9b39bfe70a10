package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrv2 *CvrV1
var lockCvrV2 sync.RWMutex
var cvrv2cron *cron.Cron

func CvrV2Instance() *CvrV1 {
	lockCvrV2.RLock()
	defer lockCvrV2.RUnlock()
	return cvrv2
}

//type CvrV2 struct {
//	*LightgbmBase
//}

const CvrV2Name = "all-tagid-cvr"
const CvrV2BigVersion = "v2"
const CvrV2LocalFilePath = "./depend/modelfile/cvrV2/model.tar.gz"
const CvrV2FeatureSignPath = "./depend/modelfile/cvrV2/feature_sign.txt"
const CvrV2InstallListPath = "./depend/modelfile/cvrV2/install_list.txt"
const CvrV2ModelFilePath = "./depend/modelfile/cvrV2/local_cvr_model.txt"
const CvrV2CaliFilePath = "./depend/modelfile/cvrV2/cali_cvr_model.json"
const CvrV2ChecksumFilePath = "./depend/modelfile/cvrV2/checksum"

func init() {
	cvrv2 = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCvrV2()
	if err != nil {
		panic(err)
	}
	cvrv2cron = cron.New()
	err = cvrv2cron.AddFunc("0 10,30,50 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCvrV2()
		if err != nil {
			logger.Instance().Error("cvrv2 reload err %v", err)
		}
	})
	cvrv2cron.Start()
}

func reloadCvrV2() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrV2Name, CvrV2BigVersion)
	if err != nil {
		return err
	}
	if ver == cvrv2.Version {
		logger.Instance().Info("no need to reload cvrV2, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrV2LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrV2ChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrV2FeatureSignPath,
			installListPath:  CvrV2InstallListPath,
			modelFilePath:    CvrV2ModelFilePath,
			caliFilePath:     CvrV2CaliFilePath,
			checksumFilePath: CvrV2ChecksumFilePath,
			feaList:          feaCatV3,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrV2.Lock()
	defer lockCvrV2.Unlock()
	cvrv2 = cvr
	logger.Instance().Info("reload cvrV2 success")
	return nil
}
