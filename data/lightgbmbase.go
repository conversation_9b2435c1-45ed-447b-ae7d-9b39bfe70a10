package data

import (
	"bufio"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"strings"

	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
)

const LightgbmCtrType = 0
const LightgbmCvrType = 1
const LightgbmBiddingType = 2

type LightgbmBase struct {
	featureSignPath  string
	installListPath  string
	modelFilePath    string
	caliFilePath     string
	checksumFilePath string

	PriorCTR float64
	Alpha    float64
	Beta     float64

	FeatureMap  map[string]Feature
	CategoryMap CatTable // 标签转id使用
	AppIndex    map[string]int

	Model       gl.Booster
	NumFeatures int
	Version     string

	// 负样本率
	NegSampleRate float64

	// 校准
	IsotonicModel *Isotonic
	feaList       []string
}

//featureSignPath "./depend/modelfile/ctr/feature_sign.txt"
//installListPath "./depend/modelfile/ctr/install_list.txt"
//modelFilePath   "./depend/modelfile/ctr/local_ctr_model.txt"

func (b *LightgbmBase) Init(modelType int) error {
	statsData, err := loadCSV(b.featureSignPath)
	if err != nil {
		return err
	}
	b.FeatureMap = make(map[string]Feature)
	b.PriorCTR = 0.01
	if b.Alpha == 0 || b.Beta == 0 {
		b.Alpha, b.Beta = 0.1, 500.0
	}
	offset := modelType
	if offset > 1 {
		offset = 0
	}
	for _, row := range statsData {
		show, _ := strconv.ParseFloat(row[1+offset], 64)
		click, _ := strconv.ParseFloat(row[2+offset], 64)
		b.FeatureMap[row[0]] = Feature{Feasign: row[0], ShowStats: show, ClickStats: click}
		if row[0] == "total_stat" {
			globalShow, _ := strconv.ParseFloat(row[1+offset], 64)
			globalClick, _ := strconv.ParseFloat(row[2+offset], 64)
			b.PriorCTR = globalClick / globalShow
		}
	}
	b.AppIndex = ReadApplist(b.installListPath)
	catStr, err := ReadPandasCategorical(b.modelFilePath)
	var tmp [][]interface{}
	err = json.Unmarshal([]byte(catStr), &tmp)
	if err != nil {
		return err
	}
	b.CategoryMap = make(CatTable)

	feas := feaCatLegacy
	if modelType == LightgbmBiddingType {
		feas = bidFeas
	}
	if len(b.feaList) != 0 {
		feas = b.feaList
	}

	for i, id := range feas {
		b.CategoryMap[id] = make(map[string]float32)
		for j, val := range tmp[i] {
			switch val := val.(type) {
			case string:
				b.CategoryMap[id][val] = float32(j)
			case int, int32, int64, float32, float64:
				b.CategoryMap[id][fmt.Sprintf("%v", val)] = float32(j)
			default:
				// 暂时没有其他的要处理
			}
		}
	}

	var cErr int
	iter := int32(0)
	var tmpModel gl.Booster
	cErr = gl.BoosterCreateFromModelfile(b.modelFilePath, &iter, &tmpModel)
	if cErr != 0 {
		logger.Instance().Error("load lightgbm model error, code: %d, path: %s", cErr, b.modelFilePath)
		return err
	}
	b.Model = tmpModel
	var numFeatures int32
	cErr = gl.BoosterGetNumFeature(b.Model, &numFeatures)
	b.NumFeatures = int(numFeatures)
	logger.Instance().Info("model features num: %d", numFeatures)

	jsonFile, err := os.Open(b.caliFilePath)
	if err != nil {
		logger.Instance().Error("load cali model error, code: %v, path: %s", err, b.caliFilePath)
		return err
	}
	defer jsonFile.Close()
	byteValue, _ := ioutil.ReadAll(jsonFile)
	var im Isotonic
	err = json.Unmarshal(byteValue, &im)
	if err != nil {
		logger.Instance().Error("load cali model error, code: %v, path: %s", err, b.caliFilePath)
		return err
	}
	b.IsotonicModel = &im

	checkFile, err := os.Open(b.checksumFilePath)
	if err != nil {
		logger.Instance().Error("load check file error, code: %v, path: %s", err, b.checksumFilePath)
		return err
	}
	defer checkFile.Close()
	byteCheck, _ := ioutil.ReadAll(checkFile)
	var check ChecksumFile
	err = json.Unmarshal(byteCheck, &check)
	if err != nil {
		logger.Instance().Error("load check file error, code: %v, path: %s", err, b.checksumFilePath)
	}
	b.NegSampleRate = check.NegSampleRate
	logger.Instance().Info("model init success, ver = %s, NegSampleRate = %f, file: %s", b.Version, b.NegSampleRate, b.modelFilePath)

	return nil
}

type Categories [][]interface{}

//var Categories_str = `[["104dac5750cf9d87"], [336, 1007, 1097, 1447, 1539, 1880, 1897, 1920, 2123, 2128, 2167, 2366, 2387, 2624, 2715, 2806, 2820, 2997, 3055, 3063, 3065, 3066, 3131, 3386, 3404, 3433, 3908, 3910, 4017, 4129, 4130, 4156, 4356, 4409, 4431, 4432, 4433, 4434, 4464], [223, 2371, 2372, 2373, 2374, 2662, 2663, 3764, 3930, 3931, 4985, 5056, 5057, 5058, 5113, 5115, 5547, 5555, 5634, 5635, 5636, 6761, 7021, 7276, 7277, 7306, 7307, 7862, 8052, 8053, 8102, 8104, 8105, 8222, 8223, 8224, 8835, 8873, 8961, 10097, 10098, 10109, 10110, 10387, 10608, 10611, 10659, 10840, 11189, 11328, 11398, 11399, 11400, 11401, 11463], ["BRA", "DEU", "IDN", "MYS", "PAK", "RUS", "SAU", "VNM"], ["Fri", "Sat"], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], ["oppo"], [0], ["Android"], ["10", "11", "12", "13", "14", "15", "5.1", "5.1.1", "6.0", "6.0.1", "7.1.1", "8.1.0", "9"], [2.0], [2], ["oppo-1004470"], ["com.alibaba.aliexpresshd", "com.amazon.mShop.android.shopping", "com.arcsoft.perfect365", "com.fugo.wow", "com.gojek.gopaymerchant", "com.hwsj.club", "com.king.candycrushsaga", "com.kubi.kucoin", "com.live.soulchill", "com.voicemaker.android", "com.xparty.androidapp", "com.yandex.searchapp", "com.zzkko", "ru.cvetov.app", "ru.ekonika.app", "ru.gibdd_pay.app", "ru.tinkoff.bnpl", "ru.uteka.app", "ru.yandex.music", "ru.yandex.yandexmaps", "sg.bigo.live"], ["GPID"], ["+00:00"], [5.0, 6.0], [39.0, 152.0, 428.0, 558.0, 600.0, 757.0, 794.0, 810.0, 812.0, 890.0, 892.0, 933.0, 938.0, 1059.0, 1067.0, 1195.0, 1207.0, 1232.0, 1269.0, 1281.0, 1320.0, 1393.0, 1397.0, 1399.0, 1400.0, 1416.0, 1805.0, 1807.0, 1865.0, 1921.0, 1922.0, 1935.0, 2046.0, 2061.0, 2073.0, 2074.0, 2075.0, 2076.0, 2083.0], [2.0], [527.0, 778.0, 796.0, 876.0, 888.0, 981.0, 990.0, 1001.0, 1013.0, 1043.0, 1045.0, 1067.0, 1071.0, 1270.0, 1284.0, 1312.0, 1338.0, 1343.0, 1380.0, 1453.0, 1457.0, 1458.0, 1459.0, 1478.0, 1481.0, 1786.0, 1787.0, 1830.0, 1881.0, 1882.0, 1888.0, 1915.0, 1950.0, 1966.0, 1977.0, 1978.0, 1979.0, 1980.0, 1987.0], [617.0, 1076.0, 1077.0, 1078.0, 1079.0, 1132.0, 1133.0, 1333.0, 1365.0, 1366.0, 1586.0, 1600.0, 1613.0, 1614.0, 1615.0, 1649.0, 1651.0, 1727.0, 1728.0, 1729.0, 1731.0, 1732.0, 1774.0, 1780.0, 2180.0, 2199.0, 2239.0, 2295.0, 2296.0, 2307.0, 2308.0, 2373.0, 2465.0, 2466.0, 2471.0, 2472.0, 2473.0, 2493.0, 2496.0, 2847.0, 2848.0, 2849.0, 2850.0, 2905.0, 2957.0, 2958.0, 2964.0, 2993.0, 3042.0, 3059.0, 3072.0, 3073.0, 3074.0, 3075.0, 3082.0], [3.0], ["300", "300x", "300x3", "300x30", "300x300"], ["3", "30", "300", "300x", "300x3", "300x30", "300x300"], [1.0, 2.0, 17.0, 69.0, 89.0, 229.0, 303.0, 710.0, 753.0, 941.0, 989.0, 1190.0, 1228.0, 1245.0, 1247.0, 1341.0, 1343.0, 1383.0, 1388.0, 1483.0, 1547.0, 1558.0, 1699.0, 1727.0, 1767.0, 1834.0, 1849.0, 1909.0, 2060.0, 2065.0, 2067.0, 2068.0, 2084.0, 2354.0, 2452.0, 2462.0], [0.0]]`

type CatTable map[string]map[string]float32

func (c *CatTable) GetVal(fea, key string) float32 {
	if _, ok := (*c)[fea][key]; ok {
		return (*c)[fea][key]
	}
	return -1
}

const PandasCategorical = "pandas_categorical:"

func ReadPandasCategorical(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err.Error() == "bufio: buffer full" {
				// 处理超长行情况，继续读取剩余内容以清空缓冲区
				continue
			}
			if err.Error() != "EOF" {
				return "", fmt.Errorf("error reading file: %v", err)
			}
			break
		}
		if strings.HasPrefix(line, PandasCategorical) {
			return strings.TrimPrefix(line, PandasCategorical), nil
		}
	}

	return "", fmt.Errorf("can not find datapandas_categorical")
}

func ReadApplist(name string) map[string]int {
	file, err := os.Open(name)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return nil
	}
	defer file.Close()

	m := make(map[string]int)
	scanner := bufio.NewScanner(file)
	i := 0
	for scanner.Scan() {
		line := scanner.Text()
		l := strings.Split(line, "\t")
		if len(l) == 2 {
			m[l[0]] = i
			i++
		}
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	return m
}

// feaCatLegacy 是为旧模型（如 ctr4470）准备的特征列表，包含26个特征
var feaCatLegacy = []string{
	"channel_id",
	"ad_id",
	"creative_id",
	"country",
	"day_of_week",
	"event_hour",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"advertiser_type",
	"tag_id",
	"package_name",
	"device_id_type",
	"timezone",
	"account_id",
	"project_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"traffic_material_size",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
}

// feaCatV3 是为您的新模型准备的特征列表，包含了新增的 app_id 和 device_model
var feaCatV3 = []string{
	"channel_id",
	"ad_id",
	"creative_id",
	"country",
	"day_of_week",
	"event_hour",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"advertiser_type",
	"app_id", // 新增的分类特征
	"tag_id",
	"package_name",
	"device_id_type",
	"timezone",
	"account_id",
	"project_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"traffic_material_size",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"device_model", // 新增的分类特征
}

// feaCatV4 新增了ad_uid
var feaCatV4 = []string{
	"channel_id",
	"ad_id",
	"ad_uid",
	"creative_id",
	"country",
	"day_of_week",
	"event_hour",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"advertiser_type",
	"app_id", // 新增的分类特征
	"tag_id",
	"package_name",
	"device_id_type",
	"timezone",
	"account_id",
	"project_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"traffic_material_size",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"device_model", // 新增的分类特征
}

// feaCatCPA 新增了四个特征
var feaCatCPA = []string{
	"channel_id",
	"ad_id",
	"creative_id",
	"country",
	"day_of_week",
	"event_hour",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"advertiser_type",
	"app_id",
	"tag_id",
	"package_name",
	"device_id_type",
	"timezone",
	"account_id",
	"project_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"traffic_material_size",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"device_model",
	"conversion_type",
	"industry_ids",
	"iab_id",
	"iab_sub_id",
}

func loadCSV(filename string) ([][]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '\t' // TSV 文件
	return reader.ReadAll()
}

type Feature struct {
	Feasign    string
	ShowStats  float64
	ClickStats float64
}

type ChecksumFile struct {
	Message       string  `json:"message"`
	NegSampleRate float64 `json:"neg_sample_rate"`
}

var bidFeas = []string{
	"channel_id",
	"country",
	"device_brand",
	"device_type",
	"os",
	"os_version",
	"network_type",
	"app_id",
	"ad_id",
	"material_id_type",
	"title_id",
	"description_id",
	"delivery_type",
	"ad_slot_type",
	"tag_id",
	"day_of_week",
	"event_hour",
	"timezone",
	"account_id",
	"project_id",
	"delivery_material_size",
	"image_material_id",
	"video_material_id",
	"traffic_material_size",
	"device_model",
	"delivery_package",
}
