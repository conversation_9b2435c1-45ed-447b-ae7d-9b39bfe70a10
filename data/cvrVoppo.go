package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var cvrOppo *CvrV1
var lockCvrOppo sync.RWMutex
var cvrOppocron *cron.Cron

func CvrOppoInstance() *CvrV1 {
	lockCvrOppo.RLock()
	defer lockCvrOppo.RUnlock()
	return cvrOppo
}

//type CvrV2 struct {
//	*LightgbmBase
//}

const CvrOppoName = "oppo-spec-cvr"
const CvrOppoBigVersion = "v2"
const CvrOppoLocalFilePath = "./depend/modelfile/cvroppo/model.tar.gz"
const CvrOppoFeatureSignPath = "./depend/modelfile/cvroppo/oppo_spec_feature_sign.txt"
const CvrOppoInstallListPath = "./depend/modelfile/cvroppo/oppo_spec_install_list.txt"
const CvrOppoModelFilePath = "./depend/modelfile/cvroppo/oppo_spec_local_cvr_model.txt"
const CvrOppoCaliFilePath = "./depend/modelfile/cvroppo/oppo_spec_cali_cvr_model.json"
const CvrOppoChecksumFilePath = "./depend/modelfile/cvroppo/checksum"

func init() {
	cvrOppo = &CvrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadOppoCPA()
	if err != nil {
		panic(err)
	}
	cvrOppocron = cron.New()
	err = cvrOppocron.AddFunc("0 10,30,50 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadOppoCPA()
		if err != nil {
			logger.Instance().Error("cvrOppo reload err %v", err)
		}
	})
	cvrOppocron.Start()
}

func reloadOppoCPA() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CvrOppoName, CvrOppoBigVersion)
	if err != nil {
		return err
	}
	if ver == cvrOppo.Version {
		logger.Instance().Info("no need to reload cvrOppo, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CvrOppoLocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CvrOppoChecksumFilePath)
	if err != nil {
		return err
	}
	cvr := &CvrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CvrOppoFeatureSignPath,
			installListPath:  CvrOppoInstallListPath,
			modelFilePath:    CvrOppoModelFilePath,
			caliFilePath:     CvrOppoCaliFilePath,
			checksumFilePath: CvrOppoChecksumFilePath,
			feaList:          feaCatCPA,
			Alpha:            SmoothAlpha,
			Beta:             SmoothBeta,
		},
	}
	err = cvr.Init(LightgbmCvrType)
	if err != nil {
		return err
	}
	lockCvrOppo.Lock()
	defer lockCvrOppo.Unlock()
	cvrOppo = cvr
	logger.Instance().Info("reload cvrOppo success")
	return nil
}
