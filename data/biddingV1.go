package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	gl "gitlab.ydmob.com/algorithm/golightly"
	"math/rand"
	"sync"
	"time"
)

const FastConfigPoolSize = 10240
const BidFeaNum = 108

var biddingV1 *BiddingV1
var lockBiddingV1 sync.RWMutex
var biddingV1cron *cron.Cron

func BiddingV1Instance() *BiddingV1 {
	lockBiddingV1.RLock()
	defer lockBiddingV1.RUnlock()
	return biddingV1
}

type BiddingV1 struct {
	*LightgbmBase
	FcPool chan gl.FastConfig
}

// BiddingV1Name 暂时逻辑是一致的
const BiddingV1Name = "all-tagid-bidding"
const BiddingV1BigVersion = "v1"
const BiddingV1LocalFilePath = "./depend/modelfile/bidding/model.tar.gz"
const BiddingFeatureSignPath = "./depend/modelfile/bidding/feature_sign_bidding.txt"
const BiddingInstallListPath = "./depend/modelfile/bidding/install_list.txt"
const BiddingModelFilePath = "./depend/modelfile/bidding/local_bidding_model.txt"
const BiddingV1CaliFilePath = "./depend/modelfile/bidding/cali_bidding_model.json"
const BiddingV1ChecksumFilePath = "./depend/modelfile/bidding/checksum"

func init() {
	gl.InitializeLightgbm("./depend/lib_lightgbm.so")
	biddingV1 = &BiddingV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadBiddingV1()
	if err != nil {
		panic(err)
	}
	biddingV1cron = cron.New()
	err = biddingV1cron.AddFunc("0 15,35,55 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadBiddingV1()
		if err != nil {
			logger.Instance().Error("biddingv1 reload err %v", err)
		}
	})
	biddingV1cron.Start()
}

func reloadBiddingV1() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(BiddingV1Name, BiddingV1BigVersion)
	if err != nil {
		return err
	}
	if ver == biddingV1.Version {
		logger.Instance().Info("no need to reload biddingV1, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, BiddingV1LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, BiddingV1ChecksumFilePath)
	if err != nil {
		return err
	}
	bidding := &BiddingV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  BiddingFeatureSignPath,
			installListPath:  BiddingInstallListPath,
			modelFilePath:    BiddingModelFilePath,
			caliFilePath:     BiddingV1CaliFilePath,
			checksumFilePath: BiddingV1ChecksumFilePath,
		},
		FcPool: make(chan gl.FastConfig, FastConfigPoolSize),
	}
	err = bidding.Init(LightgbmBiddingType)
	if err != nil {
		return err
	}
	for i := 0; i < FastConfigPoolSize; i++ {
		var fc gl.FastConfig
		cErr := gl.BoosterPredictForMatSingleRowFastInit(bidding.Model, gl.PredictNormal, 0, -1, gl.Dtype_float32, BidFeaNum, "", &fc)
		if cErr != 0 {
			panic("BoosterPredictForMatSingleRowFastInit error")
		}
		bidding.FcPool <- fc
	}
	lockBiddingV1.Lock()
	defer lockBiddingV1.Unlock()
	biddingV1 = bidding
	logger.Instance().Info("reload biddingV1 success")
	return nil
}
