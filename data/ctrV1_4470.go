package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

var ctr4470v1 *CtrV1
var lockCtr4470V1 sync.RWMutex
var ctr4470v1cron *cron.Cron

func Ctr4470V1Instance() *CtrV1 {
	lockCtr4470V1.RLock()
	defer lockCtr4470V1.RUnlock()
	return ctr4470v1
}

//type Ctr4470V1 struct {
//	*LightgbmBase
//}

const Ctr4470V1Name = "oppo-1004470-ctr"
const Ctr4470V1BigVersion = "v1"
const Ctr4470V1LocalFilePath = "./depend/modelfile/ctr4470/model.tar.gz"
const Ctr4470V1CaliFilePath = "./depend/modelfile/ctr4470/cali_ctr_model_4470.json"
const Ctr4470V1ChecksumFilePath = "./depend/modelfile/ctr4470/checksum"
const Ctr4470FeatureSignPath = "./depend/modelfile/ctr4470/feature_sign.txt"
const Ctr4470InstallListPath = "./depend/modelfile/ctr4470/install_list.txt"
const Ctr4470ModelFilePath = "./depend/modelfile/ctr4470/local_ctr_model_4470.txt"

func init() {
	ctr4470v1 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtr4470V1()
	if err != nil {
		panic(err)
	}
	ctr4470v1cron = cron.New()
	err = ctr4470v1cron.AddFunc("0 17,37,57 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtr4470V1()
		if err != nil {
			logger.Instance().Error("ctr4470v1 reload err %v", err)
		}
	})
	ctr4470v1cron.Start()
}

func reloadCtr4470V1() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(Ctr4470V1Name, Ctr4470V1BigVersion)
	if err != nil {
		return err
	}
	if ver == ctr4470v1.Version {
		logger.Instance().Info("no need to reload ctrV1, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, Ctr4470V1LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, Ctr4470V1ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  Ctr4470FeatureSignPath,
			installListPath:  Ctr4470InstallListPath,
			modelFilePath:    Ctr4470ModelFilePath,
			caliFilePath:     Ctr4470V1CaliFilePath,
			checksumFilePath: Ctr4470V1ChecksumFilePath,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtr4470V1.Lock()
	defer lockCtr4470V1.Unlock()
	ctr4470v1 = ctr
	logger.Instance().Info("reload ctr4470V1 success")
	return nil
}
