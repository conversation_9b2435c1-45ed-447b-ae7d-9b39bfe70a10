package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math/rand"
	"sync"
	"time"
)

const SmoothAlpha = 0.1
const SmoothBeta = 10

var ctrv5 *CtrV1
var lockCtrV5 sync.RWMutex
var ctrv5cron *cron.Cron

func CtrV5Instance() *CtrV1 {
	lockCtrV5.RLock()
	defer lockCtrV5.RUnlock()
	return ctrv5
}

const CtrV5Name = "all-tagid-ctr"
const CtrV5BigVersion = "v5"
const CtrV5LocalFilePath = "./depend/modelfile/ctrV5/model.tar.gz"
const CtrV5CaliFilePath = "./depend/modelfile/ctrV5/cali_ctr_model.json"
const CtrV5ChecksumFilePath = "./depend/modelfile/ctrV5/checksum"
const CtrV5FeatureSignPath = "./depend/modelfile/ctrV5/feature_sign.txt"
const CtrV5InstallListPath = "./depend/modelfile/ctrV5/install_list.txt"
const CtrV5ModelFilePath = "./depend/modelfile/ctrV5/local_ctr_model.txt"

func init() {
	ctrv5 = &CtrV1{
		LightgbmBase: &LightgbmBase{},
	}
	err := reloadCtrV5()
	if err != nil {
		panic(err)
	}
	ctrv5cron = cron.New()
	err = ctrv5cron.AddFunc("0 07,27,47 * * * ?", func() {
		// 添加随机
		time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
		err := reloadCtrV5()
		if err != nil {
			logger.Instance().Error("ctrv5 reload err %v", err)
		}
	})
	ctrv5cron.Start()
}

func reloadCtrV5() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrV5Name, CtrV5BigVersion)
	if err != nil {
		return err
	}
	if ver == ctrv5.Version {
		logger.Instance().Info("no need to reload ctrV5, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrV5LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrV5ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrV5FeatureSignPath,
			installListPath:  CtrV5InstallListPath,
			modelFilePath:    CtrV5ModelFilePath,
			caliFilePath:     CtrV5CaliFilePath,
			checksumFilePath: CtrV5ChecksumFilePath,
			feaList:          feaCatV3,
			Alpha:            SmoothAlpha,
			Beta:             SmoothBeta,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrV5.Lock()
	defer lockCtrV5.Unlock()
	ctrv5 = ctr
	logger.Instance().Info("reload ctrV5 success")
	return nil
}
