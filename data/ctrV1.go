package data

import (
	"github.com/robfig/cron"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"sync"
)

//deprecated, already update

var ctrv1 *CtrV1
var lockCtrV1 sync.RWMutex
var ctrv1cron *cron.Cron

func CtrV1Instance() *CtrV1 {
	lockCtrV1.RLock()
	defer lockCtrV1.RUnlock()
	return ctrv1
}

type CtrV1 struct {
	*LightgbmBase
}

const CtrV1Name = "all-tagid-ctr"
const CtrV1BigVersion = "v1"
const CtrV1LocalFilePath = "./depend/modelfile/ctr/model.tar.gz"
const CtrV1CaliFilePath = "./depend/modelfile/ctr/cali_ctr_model.json"
const CtrV1ChecksumFilePath = "./depend/modelfile/ctr/checksum"
const CtrFeatureSignPath = "./depend/modelfile/ctr/feature_sign.txt"
const CtrInstallListPath = "./depend/modelfile/ctr/install_list.txt"
const CtrModelFilePath = "./depend/modelfile/ctr/local_ctr_model.txt"

func init() {
	//ctrv1 = &CtrV1{
	//	LightgbmBase: &LightgbmBase{},
	//}
	//err := reloadCtrV1()
	//if err != nil {
	//	panic(err)
	//}
	//ctrv1cron = cron.New()
	//err = ctrv1cron.AddFunc("0 17,37,57 * * * ?", func() {
	//	// 添加随机
	//	time.Sleep(time.Duration(rand.Int31n(20)) * time.Second)
	//	err := reloadCtrV1()
	//	if err != nil {
	//		logger.Instance().Error("ctrv1 reload err %v", err)
	//	}
	//})
	//ctrv1cron.Start()
}

func reloadCtrV1() error {
	ver, file, checksum, err := OssVersionInstance().GetVersionModelFile(CtrV1Name, CtrV1BigVersion)
	if err != nil {
		return err
	}
	if ver == ctrv1.Version {
		logger.Instance().Info("no need to reload ctrV1, ver = %s", ver)
		return nil
	}
	err = OssVersionInstance().DownloadAndUnTar(file, CtrV1LocalFilePath)
	if err != nil {
		return err
	}
	err = OssVersionInstance().Download(checksum, CtrV1ChecksumFilePath)
	if err != nil {
		return err
	}
	ctr := &CtrV1{
		LightgbmBase: &LightgbmBase{
			Version:          ver,
			featureSignPath:  CtrFeatureSignPath,
			installListPath:  CtrInstallListPath,
			modelFilePath:    CtrModelFilePath,
			caliFilePath:     CtrV1CaliFilePath,
			checksumFilePath: CtrV1ChecksumFilePath,
		},
	}
	err = ctr.Init(LightgbmCtrType)
	if err != nil {
		return err
	}
	lockCtrV1.Lock()
	defer lockCtrV1.Unlock()
	ctrv1 = ctr
	logger.Instance().Info("reload ctrV1 success")
	return nil
}
