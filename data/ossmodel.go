package data

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"io"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

type OssVersion struct {
	bucket *oss.Bucket
}

var version *OssVersion

func init() {

}

func OssVersionInstance() *OssVersion {
	if version == nil {
		version = &OssVersion{}
		if err := version.Init(); err != nil {
			panic(err)
		}
	}
	return version
}

func (o *OssVersion) Init() error {
	provider, err := oss.NewEnvironmentVariableCredentialsProvider()
	if err != nil {
		logger.Instance().Error("Failed to create credentials provider: %v", err)
		return err
	}
	clientOptions := []oss.ClientOption{oss.SetCredentialsProvider(&provider)}
	clientOptions = append(clientOptions, oss.Region("ap-southeast-1"))
	// 设置签名版本
	clientOptions = append(clientOptions, oss.AuthVersion(oss.AuthV4))
	client, err := oss.New("oss-ap-southeast-1-internal.aliyuncs.com", "LTAI5tKtWdBjaWePTjSucfwo", "******************************", clientOptions...)
	if err != nil {
		logger.Instance().Error("Failed to create OSS client: %v", err)
		return err
	}

	o.bucket, err = client.Bucket("oversea-backup")
	if err != nil {
		logger.Instance().Error("Failed to get bucket: %v", err)
		return err
	}
	return nil
}

func (o *OssVersion) GetVersionModelFile(name, bigver string) (string, string, string, error) {
	var files []string
	marker := ""
	folderPrefix := fmt.Sprintf("models/%s/%s/", name, bigver)
	for {
		lsRes, err := o.bucket.ListObjects(oss.Prefix(folderPrefix), oss.Marker(marker), oss.MaxKeys(100))
		if err != nil {
			logger.Instance().Error("列举对象失败: %v", err)
			return "", "", "", err
		}

		for _, object := range lsRes.Objects {
			files = append(files, object.Key)
		}

		if !lsRes.IsTruncated {
			break
		}
		marker = lsRes.NextMarker
	}

	// 按照时间戳降序排序
	sort.Sort(sort.Reverse(sort.StringSlice(files)))
	checksumName := ""
	for _, name := range files {
		if strings.HasSuffix(name, "checksum") {
			checksumName = name
			break
		}
	}

	body, err := o.bucket.GetObject(checksumName)
	if err != nil {
		logger.Instance().Error("获取文件失败:", err)
		return "", "", "", err
	}
	defer body.Close()

	buf := new(bytes.Buffer)
	_, err = io.CopyN(buf, body, 1<<20) // 最多读取1MB
	if err != nil && err != io.EOF {
		logger.Instance().Error("读取文件失败:", err)
		return "", "", "", err
	}

	if !isJSON(buf.Bytes()) {
		logger.Instance().Error("checksum文件 %s 校验失败", checksumName)
		return "", "", "", fmt.Errorf("checksum文件 %s 校验失败", checksumName)
	}

	version := getVersion(checksumName)
	modelFile := strings.ReplaceAll(checksumName, "checksum", "model.tar.gz")
	return version, modelFile, checksumName, nil
}

func (o *OssVersion) DownloadAndUnTar(remote, local string) error {
	//localFilePath := "./depend/modelfile/ctr/model.tar.gz"
	logger.Instance().Info("下载model文件 %s", remote)
	err := o.bucket.GetObjectToFile(remote, local)
	if err != nil {
		logger.Instance().Info("model文件下载失败: %v", err)
		return err
	}
	err = unTar(local)
	if err != nil {
		logger.Instance().Error("解压文件失败: %v", err)
	}
	return nil
}

func (o *OssVersion) Download(remote, local string) error {
	//localFilePath := "./depend/modelfile/ctr/checksum"
	err := o.bucket.GetObjectToFile(remote, local)
	if err != nil {
		logger.Instance().Info("文件下载失败: %v", err)
		return err
	}
	return nil
}

// isJSON 检查字节切片是否为有效JSON
func isJSON(data []byte) bool {
	var js json.RawMessage
	return json.Unmarshal(data, &js) == nil
}

func getVersion(path string) string {
	parts := strings.Split(path, "/")
	if len(parts) >= 4 {
		return parts[3]
	}
	return ""
}

func unTar(tarGzFile string) error {

	file, err := os.Open(tarGzFile)
	if err != nil {
		log.Fatal("打开文件失败:", err)
		return err
	}
	defer file.Close()

	gzr, err := gzip.NewReader(file)
	if err != nil {
		log.Fatal("创建gzip reader失败:", err)
		return err
	}
	defer gzr.Close()

	tr := tar.NewReader(gzr)

	baseDir := filepath.Dir(tarGzFile)

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break // 文件结束
		}
		if err != nil {
			log.Fatal("读取tar文件失败:", err)
			return err
		}

		cleanName := filepath.Base(header.Name) // 只保留文件名部分

		// 构建目标路径（与tar.gz同目录）
		targetPath := filepath.Join(baseDir, cleanName)

		// 安全检查：确保目标路径在预期范围内
		if !strings.HasPrefix(targetPath, baseDir) {
			log.Fatalf("非法路径: %s", targetPath)
			return err
		}

		// 创建文件（自动覆盖已存在的文件）
		file, err := os.OpenFile(targetPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, os.FileMode(header.Mode))
		if err != nil {
			log.Fatal("创建文件失败:", err)
			return err
		}

		// 写入文件内容
		if _, err := io.Copy(file, tr); err != nil {
			file.Close()
			log.Fatal("写入文件失败:", err)
			return err
		}
		file.Close()
	}

	fmt.Println("解压完成，所有文件已输出到", baseDir)
	return nil
}
