package service

import (
	"context"
	"encoding/json"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/server"
	m "gitlab.ydmob.com/algorithm/brief-framework/models"
	"gitlab.ydmob.com/algorithm/brief-framework/server_plugin"
)

func init() {
	server_plugin.AddProcess("/rank", rank)
}

var rank server_plugin.PostHandler = func(data []byte) interface{} {
	req := ranker.AdRequestData{}
	err := json.Unmarshal(data, &req)
	if err != nil {
		return m.Ret{
			ReturnCode: 1,
			Message:    "json parse error",
		}
	}
	rs := server.RankServer{}
	response, err := rs.Rank(context.Background(), &req)
	if err != nil {
		return m.Ret{
			ReturnCode: 1,
			Message:    err.Error(),
		}
	}

	return response
}
