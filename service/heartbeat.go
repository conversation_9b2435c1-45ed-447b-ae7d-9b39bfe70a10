package service

import (
	m "gitlab.ydmob.com/algorithm/brief-framework/models"
	"gitlab.ydmob.com/algorithm/brief-framework/server_plugin"
	"time"
)

func init() {
	server_plugin.AddProcess("/heartbeat", heartbeat)
}

var heartbeat server_plugin.GetHandle = func(reqMap map[string]string) interface{} {
	return m.Ret{
		ReturnCode: 0,
		Message:    "OK",
		Result: map[string]interface{}{
			"time": time.Now().Unix(),
		},
	}
}
