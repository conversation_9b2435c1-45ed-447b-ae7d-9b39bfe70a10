# Oppo广告位问题修复说明

## 问题概述

在测试 Oppo 广告位（`oppo-1004916` 和 `oppo-1004804`）时遇到两个主要错误：

1. **rankv2.go 第67行**：`predict cvr error, code: 4294967295` + LightGBM特征数量不匹配
2. **rankv1.go 第71行**：`bidding data is nil`

## 根本原因

两个问题的根本原因都是 **ExpAds 键值不匹配**：

### 问题1：rankv2.go
- **ExpAds 设置**：根据 `ModelGroup="2"` 设置 `rc.ExpAds["2"]`
- **Oppo 处理**：特征处理器期望 key 为 `"oppo-1004916"`
- **结果**：特征处理器在错误的 key 下创建数据，预测器访问失败

### 问题2：rankv1.go  
- **ExpAds 设置**：根据 `ab_test_ad="84_271_5"` 设置 `rc.ExpAds["5_cpa"]`
- **Oppo 处理**：特征处理器期望 key 为 `"oppo-1004916"`，但实际广告位是 `"oppo-1004804"`
- **结果**：BiddingData 初始化为空数组，预测器检查失败

## 修复方案

### 1. ExpAds 重新映射

在 Oppo 处理逻辑中，将所有现有的广告数据重新映射到正确的 Oppo key：

```go
// 重新映射 ExpAds 到 Oppo 的 key
oppoKey := req.AdSlotInfo.AdSlotId // "oppo-1004916" 或 "oppo-1004804"
if len(rc.ExpAds) > 0 {
    // 将所有现有的广告数据映射到 oppo key
    var allAds []*ranker.AdMaterial
    var allNames []string
    for key, ads := range rc.ExpAds {
        allAds = append(allAds, ads...)
        allNames = append(allNames, rc.ExpNames[key]...)
    }
    // 清空原有数据，重新设置到 oppo key
    rc.ExpAds = make(map[string][]*ranker.AdMaterial)
    rc.ExpNames = make(map[string][]string)
    rc.ExpAds[oppoKey] = allAds
    rc.ExpNames[oppoKey] = allNames
}
```

### 2. 动态 ExpName 查找

在特征处理器和预测器中，动态查找正确的 Oppo key：

```go
// 动态找到正确的 Oppo key
var actualExpName string
for key := range rc.ExpAds {
    if key == models.TagIdOppo1 || key == models.TagIdOppo2 {
        actualExpName = key
        break
    }
}

// 如果没找到 Oppo key，使用默认的
if actualExpName == "" {
    actualExpName = p.expName // 或 l.expName
}
```

## 修复的文件

### 1. server/rankv2.go
- **位置**：第58-83行
- **修改**：在 Oppo 处理逻辑中添加 ExpAds 重新映射
- **目的**：确保特征处理器和预测器使用正确的 key

### 2. server/rankv1.go  
- **位置**：第74-97行
- **修改**：在 Oppo 处理逻辑中添加 ExpAds 重新映射
- **目的**：确保特征处理器和预测器使用正确的 key

### 3. feature/process/ctcvrOppo.go
- **位置**：第30-73行
- **修改**：动态查找正确的 Oppo key，替换所有 `p.expName` 为 `actualExpName`
- **目的**：支持不同的 Oppo 广告位（oppo-1004916 和 oppo-1004804）

### 4. predict/lightgbm/ctcvrOppo.go
- **位置**：第26-28行，第30-164行
- **修改**：
  1. 修正 `expName` 为 `models.TagIdOppo1`
  2. 动态查找正确的 Oppo key，替换所有 `l.expName` 为 `actualExpName`
- **目的**：支持不同的 Oppo 广告位，确保数据访问正确

## 测试验证

### 测试用例1（rankv2.go）
```json
{
  "ad_slot_info": {"ad_slot_id": "oppo-1004916"},
  "ad_material_info": [{"model_group": "2", "conversion_type": 1}]
}
```

### 测试用例2（rankv1.go）
```json
{
  "ad_slot_info": {"ad_slot_id": "oppo-1004804"},
  "ad_material_info": [{"ab_test_ad": "84_271_5", "conversion_type": 20}]
}
```

## 预期结果

修复后，两个测试用例都应该：
1. 正确映射 ExpAds 到对应的 Oppo 广告位 key
2. 特征处理器在正确的 key 下创建特征数据
3. 预测器能够正确访问特征数据并完成预测
4. 返回正常的响应而不是错误

## 注意事项

1. **向后兼容**：修改保持了向后兼容性，不影响非 Oppo 广告位的处理
2. **日志记录**：添加了调试日志，便于问题排查
3. **错误处理**：保留了原有的错误检查逻辑
4. **性能影响**：动态查找的开销很小，不会显著影响性能
