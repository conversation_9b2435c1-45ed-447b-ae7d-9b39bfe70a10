package main

import (
	"log"
	"net"
	"os"
	"os/signal"
	"runtime/debug"
	"syscall"

	"gitlab.ydmob.com/algorithm/adranker/registry"

	"gitlab.ydmob.com/algorithm/adranker/apollo"
	_ "gitlab.ydmob.com/algorithm/adranker/config"
	_ "gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/server"
	_ "gitlab.ydmob.com/algorithm/adranker/service"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	briefframework "gitlab.ydmob.com/algorithm/brief-framework"
	"gitlab.ydmob.com/algorithm/brief-framework/config"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"google.golang.org/grpc"
)

func main() {
	debug.SetMaxThreads(100000)
	go func() {
		// 初始化apollo
		apollo.InitNS()
		// 初始化 Kafka 生产者
		kafkaBrokers := config.Instance().MustValueArray("kafka-dump", "addr", ",")
		kafkaTopic := config.Instance().MustValue("kafka-dump", "topic")
		if err := tools.InitKafkaProducer(kafkaBrokers, kafkaTopic); err != nil {
			logger.Instance().Error("Failed to initialize Kafka producer: %v", err)
		}
		defer tools.CloseKafkaProducer()
		registry.Build()

		addr := config.Instance().MustValue("online", "grpc")
		// 启动 gRPC 服务器
		lis, err := net.Listen("tcp", addr)
		if err != nil {
			log.Fatalf("failed to listen: %v", err)
		}
		s := grpc.NewServer()
		ranker.RegisterRankServiceServer(s, &server.RankServer{})
		logger.Instance().Info("Server is running at %s", addr)
		go func() {
			briefframework.FreamworkServe()
		}()
		if err := s.Serve(lis); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigs
	logger.Instance().Info("quiting with signal: %s", sig.String())
}
