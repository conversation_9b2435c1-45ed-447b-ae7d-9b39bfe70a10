// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: sort.proto

package ranker

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RankService_Rank_FullMethodName = "/rank.RankService/Rank"
)

// RankServiceClient is the client API for RankService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankServiceClient interface {
	Rank(ctx context.Context, in *AdRequestData, opts ...grpc.CallOption) (*AdResponseData, error)
}

type rankServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRankServiceClient(cc grpc.ClientConnInterface) RankServiceClient {
	return &rankServiceClient{cc}
}

func (c *rankServiceClient) Rank(ctx context.Context, in *AdRequestData, opts ...grpc.CallOption) (*AdResponseData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdResponseData)
	err := c.cc.Invoke(ctx, RankService_Rank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankServiceServer is the server API for RankService service.
// All implementations must embed UnimplementedRankServiceServer
// for forward compatibility.
type RankServiceServer interface {
	Rank(context.Context, *AdRequestData) (*AdResponseData, error)
	mustEmbedUnimplementedRankServiceServer()
}

// UnimplementedRankServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRankServiceServer struct{}

func (UnimplementedRankServiceServer) Rank(context.Context, *AdRequestData) (*AdResponseData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rank not implemented")
}
func (UnimplementedRankServiceServer) mustEmbedUnimplementedRankServiceServer() {}
func (UnimplementedRankServiceServer) testEmbeddedByValue()                     {}

// UnsafeRankServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankServiceServer will
// result in compilation errors.
type UnsafeRankServiceServer interface {
	mustEmbedUnimplementedRankServiceServer()
}

func RegisterRankServiceServer(s grpc.ServiceRegistrar, srv RankServiceServer) {
	// If the following call pancis, it indicates UnimplementedRankServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RankService_ServiceDesc, srv)
}

func _RankService_Rank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdRequestData)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).Rank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_Rank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).Rank(ctx, req.(*AdRequestData))
	}
	return interceptor(ctx, in, info, handler)
}

// RankService_ServiceDesc is the grpc.ServiceDesc for RankService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rank.RankService",
	HandlerType: (*RankServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Rank",
			Handler:    _RankService_Rank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sort.proto",
}
