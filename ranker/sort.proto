syntax = "proto3";

package rank;

option go_package = "./;ranker";
//option java_package = "com.ymob.adtarget.rtb.service.grpc";

message AdRequest {
    string device_id = 1; // 设备ID
    string brand = 3; // 设备品牌
    string model = 4; // 设备型号
    string os = 5; // 操作系统，如 Android/iOS
    string os_version = 6; // 操作系统版本
    string carrier = 7; // 运营商
    int32 network_type = 8; // 网络类型，0 = 未知，1=wifi，2=2g,3=3g,4=4g,5=5g,10=ethernet,11 = 其他
    string country_code = 9; // 国家码，例如 CN、US
    string ip = 10; // 设备 IP 地址
    int32 device_type = 11; // 设备类型，0— 手机，1— 平板，2—PC，3=tv / 户外设备
    repeated int32 install_list = 12; // 设备已安装应用列表
    repeated int32 media_blacklist = 13; // 媒体黑名单
    repeated int32 recommended_package = 14; // 推荐包名
    repeated int32 search_recommendations = 15; // 母应用列表
    string device_id_type = 16; // GPID
    string channel_id = 17; // 渠道 ID
}

message AdSlot {
    string ad_slot_id = 1; // 广告位 ID
    int32 delivery_type = 2; // 投放类型，1 banner 2 插屏 3 普通原生 4 激励视频
    string size = 3; // 广告尺寸，如 300x250
    string source_package = 4; // 来源包名
    int32 advertiser_type = 6; // 广告商类型
    double floor_price = 7; //流量底价
}

message AdMaterial {
    string ad_id = 1; // 广告 ID
    string creative_group_id = 2; // 组合素材 ID
    int32 material_type = 3; // 流量类型 1 BANNER；2 VIDEO；3 NATIVE
    string title_id = 4; // 标题 ID
    string description_id = 5; // 描述 ID
    string material_size = 6; // 素材尺寸，例如 1920x1080
    string image_material_id = 7; // 图片素材 ID
    string video_material_id = 8; // 视频素材 ID
    int32 delivery_method = 9; // 投放方式，0 匀速， 1 加速投放
    string target_package = 10; // 投放的包名
    double cpa = 11; //项目配置的转化单价
    int32 account_id = 12; // 账户 ID
    int32 project_id = 13;  // 项目 ID
    string timezone = 14; // +00:00
    string bid_id = 15; // 参与竞价id
    int32 material_id_type = 16; // 素材 ID 类型,0图片； 1视频 ； 2 只存在icon
    int32 conversion_type = 17; // 转化目标 1：安装；2激活；3注册；4：购买 ...
    double conversion_price = 18; //转化单价
    double day_budget = 19;  // 广告剩余日预算
    string ab_test_ad = 20; // 广告级别的AB测试标签
    string model_group = 21; //version为2时使用
}

message AdRequestData {
    AdRequest device_info = 1; // 设备相关信息
    AdSlot ad_slot_info = 2; // 广告位信息
    repeated AdMaterial ad_material_info = 3; // 广告素材信息
    string request_id = 4;
    string ab_test = 5;
    int32 version = 6;  //实验版传1，线上传2
}

message AdResponseItem {
    string ad_id = 1;
    double ctr = 2;
    double cvr = 3;
    string creative_group_id = 4;
    double bid = 5;
    repeated AdBidItem bid_items = 6;
}

message AdBidItem {
    string flag = 1; // 标签只对应一个实验
    double bid = 2;
    double ctr = 3;
    double cvr = 4;
    int32 traffic_exploration = 5;
}

message AdResponseData {
    uint32 state = 1;   //0表示正常
    string message = 2;   // 信息
    repeated AdResponseItem response_items = 3;
    string request_id = 4;
}

service RankService {
    rpc Rank (AdRequestData) returns (AdResponseData) {}
}