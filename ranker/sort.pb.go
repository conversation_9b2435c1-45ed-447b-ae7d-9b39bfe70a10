// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: sort.proto

package ranker

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	DeviceId              string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`                                                 // 设备ID
	Brand                 string                 `protobuf:"bytes,3,opt,name=brand,proto3" json:"brand,omitempty"`                                                                       // 设备品牌
	Model                 string                 `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`                                                                       // 设备型号
	Os                    string                 `protobuf:"bytes,5,opt,name=os,proto3" json:"os,omitempty"`                                                                             // 操作系统，如 Android/iOS
	OsVersion             string                 `protobuf:"bytes,6,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`                                              // 操作系统版本
	Carrier               string                 `protobuf:"bytes,7,opt,name=carrier,proto3" json:"carrier,omitempty"`                                                                   // 运营商
	NetworkType           int32                  `protobuf:"varint,8,opt,name=network_type,json=networkType,proto3" json:"network_type,omitempty"`                                       // 网络类型，0 = 未知，1=wifi，2=2g,3=3g,4=4g,5=5g,10=ethernet,11 = 其他
	CountryCode           string                 `protobuf:"bytes,9,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`                                        // 国家码，例如 CN、US
	Ip                    string                 `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip,omitempty"`                                                                            // 设备 IP 地址
	DeviceType            int32                  `protobuf:"varint,11,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`                                         // 设备类型，0— 手机，1— 平板，2—PC，3=tv / 户外设备
	InstallList           []int32                `protobuf:"varint,12,rep,packed,name=install_list,json=installList,proto3" json:"install_list,omitempty"`                               // 设备已安装应用列表
	MediaBlacklist        []int32                `protobuf:"varint,13,rep,packed,name=media_blacklist,json=mediaBlacklist,proto3" json:"media_blacklist,omitempty"`                      // 媒体黑名单
	RecommendedPackage    []int32                `protobuf:"varint,14,rep,packed,name=recommended_package,json=recommendedPackage,proto3" json:"recommended_package,omitempty"`          // 推荐包名
	SearchRecommendations []int32                `protobuf:"varint,15,rep,packed,name=search_recommendations,json=searchRecommendations,proto3" json:"search_recommendations,omitempty"` // 母应用列表
	DeviceIdType          string                 `protobuf:"bytes,16,opt,name=device_id_type,json=deviceIdType,proto3" json:"device_id_type,omitempty"`                                  // GPID
	ChannelId             string                 `protobuf:"bytes,17,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`                                             // 渠道 ID
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AdRequest) Reset() {
	*x = AdRequest{}
	mi := &file_sort_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdRequest) ProtoMessage() {}

func (x *AdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdRequest.ProtoReflect.Descriptor instead.
func (*AdRequest) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{0}
}

func (x *AdRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AdRequest) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *AdRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *AdRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *AdRequest) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *AdRequest) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *AdRequest) GetNetworkType() int32 {
	if x != nil {
		return x.NetworkType
	}
	return 0
}

func (x *AdRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *AdRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AdRequest) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *AdRequest) GetInstallList() []int32 {
	if x != nil {
		return x.InstallList
	}
	return nil
}

func (x *AdRequest) GetMediaBlacklist() []int32 {
	if x != nil {
		return x.MediaBlacklist
	}
	return nil
}

func (x *AdRequest) GetRecommendedPackage() []int32 {
	if x != nil {
		return x.RecommendedPackage
	}
	return nil
}

func (x *AdRequest) GetSearchRecommendations() []int32 {
	if x != nil {
		return x.SearchRecommendations
	}
	return nil
}

func (x *AdRequest) GetDeviceIdType() string {
	if x != nil {
		return x.DeviceIdType
	}
	return ""
}

func (x *AdRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

type AdSlot struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AdSlotId       string                 `protobuf:"bytes,1,opt,name=ad_slot_id,json=adSlotId,proto3" json:"ad_slot_id,omitempty"`                  // 广告位 ID
	DeliveryType   int32                  `protobuf:"varint,2,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`       // 投放类型，1 banner 2 插屏 3 普通原生 4 激励视频
	Size           string                 `protobuf:"bytes,3,opt,name=size,proto3" json:"size,omitempty"`                                            // 广告尺寸，如 300x250
	SourcePackage  string                 `protobuf:"bytes,4,opt,name=source_package,json=sourcePackage,proto3" json:"source_package,omitempty"`     // 来源包名
	AdvertiserType int32                  `protobuf:"varint,6,opt,name=advertiser_type,json=advertiserType,proto3" json:"advertiser_type,omitempty"` // 广告商类型
	FloorPrice     float64                `protobuf:"fixed64,7,opt,name=floor_price,json=floorPrice,proto3" json:"floor_price,omitempty"`            //流量底价
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdSlot) Reset() {
	*x = AdSlot{}
	mi := &file_sort_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdSlot) ProtoMessage() {}

func (x *AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdSlot.ProtoReflect.Descriptor instead.
func (*AdSlot) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{1}
}

func (x *AdSlot) GetAdSlotId() string {
	if x != nil {
		return x.AdSlotId
	}
	return ""
}

func (x *AdSlot) GetDeliveryType() int32 {
	if x != nil {
		return x.DeliveryType
	}
	return 0
}

func (x *AdSlot) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *AdSlot) GetSourcePackage() string {
	if x != nil {
		return x.SourcePackage
	}
	return ""
}

func (x *AdSlot) GetAdvertiserType() int32 {
	if x != nil {
		return x.AdvertiserType
	}
	return 0
}

func (x *AdSlot) GetFloorPrice() float64 {
	if x != nil {
		return x.FloorPrice
	}
	return 0
}

type AdMaterial struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AdId            string                 `protobuf:"bytes,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                                     // 广告 ID
	CreativeGroupId string                 `protobuf:"bytes,2,opt,name=creative_group_id,json=creativeGroupId,proto3" json:"creative_group_id,omitempty"`  // 组合素材 ID
	MaterialType    int32                  `protobuf:"varint,3,opt,name=material_type,json=materialType,proto3" json:"material_type,omitempty"`            // 流量类型 1 BANNER；2 VIDEO；3 NATIVE
	TitleId         string                 `protobuf:"bytes,4,opt,name=title_id,json=titleId,proto3" json:"title_id,omitempty"`                            // 标题 ID
	DescriptionId   string                 `protobuf:"bytes,5,opt,name=description_id,json=descriptionId,proto3" json:"description_id,omitempty"`          // 描述 ID
	MaterialSize    string                 `protobuf:"bytes,6,opt,name=material_size,json=materialSize,proto3" json:"material_size,omitempty"`             // 素材尺寸，例如 1920x1080
	ImageMaterialId string                 `protobuf:"bytes,7,opt,name=image_material_id,json=imageMaterialId,proto3" json:"image_material_id,omitempty"`  // 图片素材 ID
	VideoMaterialId string                 `protobuf:"bytes,8,opt,name=video_material_id,json=videoMaterialId,proto3" json:"video_material_id,omitempty"`  // 视频素材 ID
	DeliveryMethod  int32                  `protobuf:"varint,9,opt,name=delivery_method,json=deliveryMethod,proto3" json:"delivery_method,omitempty"`      // 投放方式，0 匀速， 1 加速投放
	TargetPackage   string                 `protobuf:"bytes,10,opt,name=target_package,json=targetPackage,proto3" json:"target_package,omitempty"`         // 投放的包名
	Cpa             float64                `protobuf:"fixed64,11,opt,name=cpa,proto3" json:"cpa,omitempty"`                                                //项目配置的转化单价
	AccountId       int32                  `protobuf:"varint,12,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                    // 账户 ID
	ProjectId       int32                  `protobuf:"varint,13,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                    // 项目 ID
	Timezone        string                 `protobuf:"bytes,14,opt,name=timezone,proto3" json:"timezone,omitempty"`                                        // +00:00
	BidId           string                 `protobuf:"bytes,15,opt,name=bid_id,json=bidId,proto3" json:"bid_id,omitempty"`                                 // 参与竞价id
	MaterialIdType  int32                  `protobuf:"varint,16,opt,name=material_id_type,json=materialIdType,proto3" json:"material_id_type,omitempty"`   // 素材 ID 类型,0图片； 1视频 ； 2 只存在icon
	ConversionType  int32                  `protobuf:"varint,17,opt,name=conversion_type,json=conversionType,proto3" json:"conversion_type,omitempty"`     // 转化目标 1：安装；2激活；3注册；4：购买 ...
	ConversionPrice float64                `protobuf:"fixed64,18,opt,name=conversion_price,json=conversionPrice,proto3" json:"conversion_price,omitempty"` //转化单价
	DayBudget       float64                `protobuf:"fixed64,19,opt,name=day_budget,json=dayBudget,proto3" json:"day_budget,omitempty"`                   // 广告剩余日预算
	AbTestAd        string                 `protobuf:"bytes,20,opt,name=ab_test_ad,json=abTestAd,proto3" json:"ab_test_ad,omitempty"`                      // 广告级别的AB测试标签
	ModelGroup      string                 `protobuf:"bytes,21,opt,name=model_group,json=modelGroup,proto3" json:"model_group,omitempty"`                  //version为2时使用
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AdMaterial) Reset() {
	*x = AdMaterial{}
	mi := &file_sort_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdMaterial) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdMaterial) ProtoMessage() {}

func (x *AdMaterial) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdMaterial.ProtoReflect.Descriptor instead.
func (*AdMaterial) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{2}
}

func (x *AdMaterial) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *AdMaterial) GetCreativeGroupId() string {
	if x != nil {
		return x.CreativeGroupId
	}
	return ""
}

func (x *AdMaterial) GetMaterialType() int32 {
	if x != nil {
		return x.MaterialType
	}
	return 0
}

func (x *AdMaterial) GetTitleId() string {
	if x != nil {
		return x.TitleId
	}
	return ""
}

func (x *AdMaterial) GetDescriptionId() string {
	if x != nil {
		return x.DescriptionId
	}
	return ""
}

func (x *AdMaterial) GetMaterialSize() string {
	if x != nil {
		return x.MaterialSize
	}
	return ""
}

func (x *AdMaterial) GetImageMaterialId() string {
	if x != nil {
		return x.ImageMaterialId
	}
	return ""
}

func (x *AdMaterial) GetVideoMaterialId() string {
	if x != nil {
		return x.VideoMaterialId
	}
	return ""
}

func (x *AdMaterial) GetDeliveryMethod() int32 {
	if x != nil {
		return x.DeliveryMethod
	}
	return 0
}

func (x *AdMaterial) GetTargetPackage() string {
	if x != nil {
		return x.TargetPackage
	}
	return ""
}

func (x *AdMaterial) GetCpa() float64 {
	if x != nil {
		return x.Cpa
	}
	return 0
}

func (x *AdMaterial) GetAccountId() int32 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AdMaterial) GetProjectId() int32 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *AdMaterial) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *AdMaterial) GetBidId() string {
	if x != nil {
		return x.BidId
	}
	return ""
}

func (x *AdMaterial) GetMaterialIdType() int32 {
	if x != nil {
		return x.MaterialIdType
	}
	return 0
}

func (x *AdMaterial) GetConversionType() int32 {
	if x != nil {
		return x.ConversionType
	}
	return 0
}

func (x *AdMaterial) GetConversionPrice() float64 {
	if x != nil {
		return x.ConversionPrice
	}
	return 0
}

func (x *AdMaterial) GetDayBudget() float64 {
	if x != nil {
		return x.DayBudget
	}
	return 0
}

func (x *AdMaterial) GetAbTestAd() string {
	if x != nil {
		return x.AbTestAd
	}
	return ""
}

func (x *AdMaterial) GetModelGroup() string {
	if x != nil {
		return x.ModelGroup
	}
	return ""
}

type AdRequestData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	DeviceInfo     *AdRequest             `protobuf:"bytes,1,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`               // 设备相关信息
	AdSlotInfo     *AdSlot                `protobuf:"bytes,2,opt,name=ad_slot_info,json=adSlotInfo,proto3" json:"ad_slot_info,omitempty"`             // 广告位信息
	AdMaterialInfo []*AdMaterial          `protobuf:"bytes,3,rep,name=ad_material_info,json=adMaterialInfo,proto3" json:"ad_material_info,omitempty"` // 广告素材信息
	RequestId      string                 `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	AbTest         string                 `protobuf:"bytes,5,opt,name=ab_test,json=abTest,proto3" json:"ab_test,omitempty"`
	Version        int32                  `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"` //实验版传1，线上传2
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AdRequestData) Reset() {
	*x = AdRequestData{}
	mi := &file_sort_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdRequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdRequestData) ProtoMessage() {}

func (x *AdRequestData) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdRequestData.ProtoReflect.Descriptor instead.
func (*AdRequestData) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{3}
}

func (x *AdRequestData) GetDeviceInfo() *AdRequest {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *AdRequestData) GetAdSlotInfo() *AdSlot {
	if x != nil {
		return x.AdSlotInfo
	}
	return nil
}

func (x *AdRequestData) GetAdMaterialInfo() []*AdMaterial {
	if x != nil {
		return x.AdMaterialInfo
	}
	return nil
}

func (x *AdRequestData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AdRequestData) GetAbTest() string {
	if x != nil {
		return x.AbTest
	}
	return ""
}

func (x *AdRequestData) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type AdResponseItem struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AdId            string                 `protobuf:"bytes,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	Ctr             float64                `protobuf:"fixed64,2,opt,name=ctr,proto3" json:"ctr,omitempty"`
	Cvr             float64                `protobuf:"fixed64,3,opt,name=cvr,proto3" json:"cvr,omitempty"`
	CreativeGroupId string                 `protobuf:"bytes,4,opt,name=creative_group_id,json=creativeGroupId,proto3" json:"creative_group_id,omitempty"`
	Bid             float64                `protobuf:"fixed64,5,opt,name=bid,proto3" json:"bid,omitempty"`
	BidItems        []*AdBidItem           `protobuf:"bytes,6,rep,name=bid_items,json=bidItems,proto3" json:"bid_items,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AdResponseItem) Reset() {
	*x = AdResponseItem{}
	mi := &file_sort_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdResponseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponseItem) ProtoMessage() {}

func (x *AdResponseItem) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponseItem.ProtoReflect.Descriptor instead.
func (*AdResponseItem) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{4}
}

func (x *AdResponseItem) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *AdResponseItem) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *AdResponseItem) GetCvr() float64 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

func (x *AdResponseItem) GetCreativeGroupId() string {
	if x != nil {
		return x.CreativeGroupId
	}
	return ""
}

func (x *AdResponseItem) GetBid() float64 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *AdResponseItem) GetBidItems() []*AdBidItem {
	if x != nil {
		return x.BidItems
	}
	return nil
}

type AdBidItem struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Flag               string                 `protobuf:"bytes,1,opt,name=flag,proto3" json:"flag,omitempty"` // 标签只对应一个实验
	Bid                float64                `protobuf:"fixed64,2,opt,name=bid,proto3" json:"bid,omitempty"`
	Ctr                float64                `protobuf:"fixed64,3,opt,name=ctr,proto3" json:"ctr,omitempty"`
	Cvr                float64                `protobuf:"fixed64,4,opt,name=cvr,proto3" json:"cvr,omitempty"`
	TrafficExploration int32                  `protobuf:"varint,5,opt,name=traffic_exploration,json=trafficExploration,proto3" json:"traffic_exploration,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AdBidItem) Reset() {
	*x = AdBidItem{}
	mi := &file_sort_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdBidItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdBidItem) ProtoMessage() {}

func (x *AdBidItem) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdBidItem.ProtoReflect.Descriptor instead.
func (*AdBidItem) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{5}
}

func (x *AdBidItem) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *AdBidItem) GetBid() float64 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *AdBidItem) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *AdBidItem) GetCvr() float64 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

func (x *AdBidItem) GetTrafficExploration() int32 {
	if x != nil {
		return x.TrafficExploration
	}
	return 0
}

type AdResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         uint32                 `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`    //0表示正常
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 信息
	ResponseItems []*AdResponseItem      `protobuf:"bytes,3,rep,name=response_items,json=responseItems,proto3" json:"response_items,omitempty"`
	RequestId     string                 `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdResponseData) Reset() {
	*x = AdResponseData{}
	mi := &file_sort_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponseData) ProtoMessage() {}

func (x *AdResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_sort_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponseData.ProtoReflect.Descriptor instead.
func (*AdResponseData) Descriptor() ([]byte, []int) {
	return file_sort_proto_rawDescGZIP(), []int{6}
}

func (x *AdResponseData) GetState() uint32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *AdResponseData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AdResponseData) GetResponseItems() []*AdResponseItem {
	if x != nil {
		return x.ResponseItems
	}
	return nil
}

func (x *AdResponseData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

var File_sort_proto protoreflect.FileDescriptor

var file_sort_proto_rawDesc = string([]byte{
	0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x22, 0x8d, 0x04, 0x0a, 0x09, 0x41, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x12, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x0f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x15, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a,
	0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x64, 0x22, 0xd0, 0x01, 0x0a, 0x06, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x1c, 0x0a,
	0x0a, 0x61, 0x64, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0xe0, 0x05, 0x0a, 0x0a, 0x41, 0x64, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x61,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x70, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61,
	0x79, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09,
	0x64, 0x61, 0x79, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x62, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x62, 0x54, 0x65, 0x73, 0x74, 0x41, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xff, 0x01, 0x0a, 0x0d, 0x41, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x0b, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x0c,
	0x61, 0x64, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74,
	0x52, 0x0a, 0x61, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x10,
	0x61, 0x64, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x0e, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x62, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x62, 0x54, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xb5, 0x01, 0x0a, 0x0e, 0x41,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x13, 0x0a,
	0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x63, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x63, 0x76, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x62, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41,
	0x64, 0x42, 0x69, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x62, 0x69, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x09, 0x41, 0x64, 0x42, 0x69, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x66, 0x6c, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x74, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x76, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9c, 0x01, 0x0a, 0x0e,
	0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b,
	0x0a, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x32, 0x42, 0x0a, 0x0b, 0x52, 0x61,
	0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x52, 0x61, 0x6e,
	0x6b, 0x12, 0x13, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x14, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x41, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x42, 0x0b,
	0x5a, 0x09, 0x2e, 0x2f, 0x3b, 0x72, 0x61, 0x6e, 0x6b, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
})

var (
	file_sort_proto_rawDescOnce sync.Once
	file_sort_proto_rawDescData []byte
)

func file_sort_proto_rawDescGZIP() []byte {
	file_sort_proto_rawDescOnce.Do(func() {
		file_sort_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_sort_proto_rawDesc), len(file_sort_proto_rawDesc)))
	})
	return file_sort_proto_rawDescData
}

var file_sort_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_sort_proto_goTypes = []any{
	(*AdRequest)(nil),      // 0: rank.AdRequest
	(*AdSlot)(nil),         // 1: rank.AdSlot
	(*AdMaterial)(nil),     // 2: rank.AdMaterial
	(*AdRequestData)(nil),  // 3: rank.AdRequestData
	(*AdResponseItem)(nil), // 4: rank.AdResponseItem
	(*AdBidItem)(nil),      // 5: rank.AdBidItem
	(*AdResponseData)(nil), // 6: rank.AdResponseData
}
var file_sort_proto_depIdxs = []int32{
	0, // 0: rank.AdRequestData.device_info:type_name -> rank.AdRequest
	1, // 1: rank.AdRequestData.ad_slot_info:type_name -> rank.AdSlot
	2, // 2: rank.AdRequestData.ad_material_info:type_name -> rank.AdMaterial
	5, // 3: rank.AdResponseItem.bid_items:type_name -> rank.AdBidItem
	4, // 4: rank.AdResponseData.response_items:type_name -> rank.AdResponseItem
	3, // 5: rank.RankService.Rank:input_type -> rank.AdRequestData
	6, // 6: rank.RankService.Rank:output_type -> rank.AdResponseData
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_sort_proto_init() }
func file_sort_proto_init() {
	if File_sort_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_sort_proto_rawDesc), len(file_sort_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sort_proto_goTypes,
		DependencyIndexes: file_sort_proto_depIdxs,
		MessageInfos:      file_sort_proto_msgTypes,
	}.Build()
	File_sort_proto = out.File
	file_sort_proto_goTypes = nil
	file_sort_proto_depIdxs = nil
}
