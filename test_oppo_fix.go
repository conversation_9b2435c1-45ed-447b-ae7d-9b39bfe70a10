package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/server"
)

func main() {
	// 测试数据 - 你提供的请求
	requestJSON := `{
		"device_info": {
			"device_id": "3e2b7bcf-e9d1-4b32-bc6d-c3b01c59d38e",
			"brand": "oppo",
			"model": "CPH2127",
			"os": "Android",
			"os_version": "10",
			"network_type": 2,
			"country_code": "IDN",
			"ip": "************",
			"recommended_package": [291916],
			"device_id_type": "GPID",
			"channel_id": "104dac5750cf9d87"
		},
		"ad_slot_info": {
			"ad_slot_id": "oppo-1004916",
			"delivery_type": 3,
			"size": "300x300",
			"source_package": "com.oppo.market",
			"advertiser_type": 2,
			"floor_price": 800
		},
		"ad_material_info": [
			{
				"ad_id": "43701",
				"creative_group_id": "27829",
				"material_type": 3,
				"title_id": "7822",
				"description_id": "10050",
				"material_size": "300x300",
				"image_material_id": "3464",
				"video_material_id": "0",
				"target_package": "com.qpon.platform",
				"cpa": 25,
				"account_id": 6,
				"project_id": 5550,
				"timezone": "+00:00",
				"bid_id": "35f525461b7447aeb5eeeb2eafb2dcf5175343723069143701",
				"material_id_type": 2,
				"conversion_type": 1,
				"day_budget": 2821.862,
				"model_group": "2"
			}
		],
		"request_id": "35f525461b7447aeb5eeeb2eafb2dcf51753437230691",
		"version": 2
	}`

	// 解析请求
	var req ranker.AdRequestData
	err := json.Unmarshal([]byte(requestJSON), &req)
	if err != nil {
		log.Fatalf("Failed to parse request: %v", err)
	}

	// 创建 RankContext
	rc := models.NewRankContext(&req)

	fmt.Printf("Testing Oppo fix with request:\n")
	fmt.Printf("- AdSlotId: %s\n", req.AdSlotInfo.AdSlotId)
	fmt.Printf("- ModelGroup: %s\n", req.AdMaterialInfo[0].ModelGroup)
	fmt.Printf("- ConversionType: %d\n", req.AdMaterialInfo[0].ConversionType)

	// 调用 RankOnline
	ctx := context.Background()
	response, err := server.RankOnline(ctx, &req, rc)

	if err != nil {
		fmt.Printf("❌ Error occurred: %v\n", err)
		
		// 检查是否是 "ctr data is nil" 错误
		if err.Error() == "ctr data is nil" {
			fmt.Printf("🔍 This is the 'ctr data is nil' error we're trying to fix\n")
		}
	} else {
		fmt.Printf("✅ Success! Response received:\n")
		fmt.Printf("- State: %d\n", response.State)
		fmt.Printf("- Message: %s\n", response.Message)
		fmt.Printf("- Response Items: %d\n", len(response.ResponseItems))
	}

	// 调试信息
	fmt.Printf("\n🔍 Debug Info:\n")
	fmt.Printf("- ExpAds keys: %v\n", getKeys(rc.ExpAds))
	fmt.Printf("- InputDataExp keys: %v\n", getKeys(rc.InputDataExp))
	
	if rc.InputDataExp != nil {
		for key, data := range rc.InputDataExp {
			if data != nil && data.CvrData != nil {
				fmt.Printf("- InputDataExp[%s].CvrData: Row=%d, Col=%d\n", 
					key, data.CvrData.Row, data.CvrData.Col)
			}
		}
	}
}

func getKeys[T any](m map[string]T) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
