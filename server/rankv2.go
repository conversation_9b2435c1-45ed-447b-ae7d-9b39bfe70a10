package server

import (
	"context"
	"fmt"
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/dump/kafkadump"
	"gitlab.ydmob.com/algorithm/adranker/feature/fea"
	"gitlab.ydmob.com/algorithm/adranker/feature/process"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/pack/adresponse"
	"gitlab.ydmob.com/algorithm/adranker/predict/lightgbm"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"strings"
)

func RankOnline(ctx context.Context, req *ranker.AdRequestData, rc *models.RankContext) (*ranker.AdResponseData, error) {

	rc.ExpAds = make(map[string][]*ranker.AdMaterial)
	rc.ExpNames = make(map[string][]string)
	// 切分实验的广告
	for _, v := range req.AdMaterialInfo {
		if v.ModelGroup == "" {
			continue
		}
		mg := v.ModelGroup
		if v.ConversionType > 1 {
			// 标记cpa模型
			rc.ExpAds[mg+models.CPASuffix] = append(rc.ExpAds[mg+models.CPASuffix], v)
			rc.ExpNames[mg+models.CPASuffix] = append(rc.ExpNames[mg+models.CPASuffix], mg)
		} else {
			rc.ExpAds[mg] = append(rc.ExpAds[mg], v)
			rc.ExpNames[mg] = append(rc.ExpNames[mg], mg)
		}
	}
	var (
		fe      = registry.GetFeaExtraction(fea.Name)
		fp      = registry.GetFeaturesProcessing(process.NameCtcvrV3)
		predict = registry.GetPredict(lightgbm.NameCtcvr3Exp)
		pack    = registry.GetResultPack(adresponse.NameExp)
		dump    = registry.GetLogDump(kafkadump.NameDumpExp)
	)

	// 获取特征是通用流程
	fe.GetFeatures(rc)

	for s := range rc.ExpAds {
		if strings.HasSuffix(s, models.CPASuffix) {
			fp = registry.GetFeaturesProcessing(process.NameCtcvrCPAGroup)
			predict = registry.GetPredict(lightgbm.NameCPA)
		} else {
			fp = registry.GetFeaturesProcessing(process.NameCtcvrV3)
			predict = registry.GetPredict(lightgbm.NameCtcvr3Exp)
		}
		// 线上的版本应该只有一个rc.ExpAds
		if req.AdSlotInfo.AdSlotId == models.TagIdOppo1 || req.AdSlotInfo.AdSlotId == models.TagIdOppo2 {
			rc.CvrV1 = data.CvrOppoInstance()
			rc.CvrCpa = data.CvrOppoInstance()
			if strings.HasSuffix(s, models.CPASuffix) {
				fp = registry.GetFeaturesProcessing(process.NameCtcvrOppo)
				predict = registry.GetPredict(lightgbm.NameOppo)
			} else {
				fp = registry.GetFeaturesProcessing(process.NameCtcvrOppoCpi)
				predict = registry.GetPredict(lightgbm.NameOppoCpi)
			}
		}
		fp.MachiningFeature(rc)
		err := predict.Predict(rc)
		if err != nil {
			logger.Instance().Error("Predict err %v, requestid = %s", err, rc.RequestId)
			rc.Response.State = 1
			rc.Response.Message = fmt.Sprintf("Predict err %v", err)
			return rc.Response, err
		}
	}
	pack.Pack(rc)
	if rc.Response.State == 0 {
		dump.Dump(rc)
	}
	if apollo.ApolloRankerConfig().ReqLogEnable() {
		logger.Instance().Info("rank response: %s", json2Str(rc.Response))
	}
	return rc.Response, nil
}
