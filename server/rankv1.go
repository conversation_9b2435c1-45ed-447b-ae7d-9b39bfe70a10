package server

import (
	"context"
	"fmt"
	"strings"

	"gitlab.ydmob.com/algorithm/adranker/data"

	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/dump/kafkadump"
	"gitlab.ydmob.com/algorithm/adranker/feature/fea"
	"gitlab.ydmob.com/algorithm/adranker/feature/process"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/pack/adresponse"
	"gitlab.ydmob.com/algorithm/adranker/predict/lightgbm"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

func RankNew(ctx context.Context, req *ranker.AdRequestData, rc *models.RankContext) (*ranker.AdResponseData, error) {
	rc.ExpAds = make(map[string][]*ranker.AdMaterial)
	rc.ExpNames = make(map[string][]string)
	// 切分实验的广告
	for _, v := range req.AdMaterialInfo {
		if v.AbTestAd == "" {
			continue
		}
		l := strings.Split(v.AbTestAd, ",")
		for _, k := range l {
			ks := strings.Split(k, "_")
			mg := ks[len(ks)-1]
			if v.ConversionType > 1 {
				// 标记cpa模型
				rc.ExpAds[mg+models.CPASuffix] = append(rc.ExpAds[mg+models.CPASuffix], v)
				rc.ExpNames[mg+models.CPASuffix] = append(rc.ExpNames[mg+models.CPASuffix], k)
			} else {
				rc.ExpAds[mg] = append(rc.ExpAds[mg], v)
				rc.ExpNames[mg] = append(rc.ExpNames[mg], k)
			}
		}
	}
	var (
		fe      = registry.GetFeaExtraction(fea.Name)
		fp      = registry.GetFeaturesProcessing(process.NameBiddingV1exp)
		predict = registry.GetPredict(lightgbm.NameBiddingExp)
		pack    = registry.GetResultPack(adresponse.NameExp)
		dump    = registry.GetLogDump(kafkadump.NameDumpExp)
	)
	//if req.AdSlotInfo.AdSlotId == models.TagId4470 {
	//	rc.CtrV1 = data.Ctr4470V1Instance()
	//}
	// 获取特征是通用流程
	fe.GetFeatures(rc)
	// TODO：目前是个串行的过程，要考虑并行，注意要加锁
	for exp := range rc.ExpAds {
		switch exp {
		case models.UidExp:
			rc.CtrV1 = data.CtrV4Instance()
			rc.CvrV1 = data.CvrV3Instance()
			fp = registry.GetFeaturesProcessing(process.NameBiddingUidExp)
			predict = registry.GetPredict(lightgbm.NameUidExp)
		//case models.Ctr4Cvr2:
		//	rc.CtrV1 = data.CtrV4Instance()
		//	rc.CvrV1 = data.CvrV2Instance()
		//	fp = registry.GetFeaturesProcessing(process.CtrV4CvrV2)
		//	predict = registry.GetPredict(lightgbm.NameCtr4Cvr2)
		case models.CPAExp:
			rc.CtrCpa = data.CtrCPAInstance()
			rc.CvrCpa = data.CvrCPAInstance()
			fp = registry.GetFeaturesProcessing(process.NameCtcvrCPAExp)
			predict = registry.GetPredict(lightgbm.NameCPAExp)
			if req.AdSlotInfo.AdSlotId == models.TagIdOppo1 || req.AdSlotInfo.AdSlotId == models.TagIdOppo2 {
				rc.CvrV1 = data.CvrOppoInstance()
				rc.CvrCpa = data.CvrOppoInstance()
				fp = registry.GetFeaturesProcessing(process.NameCtcvrExpOppo)
				predict = registry.GetPredict(lightgbm.NameOppoExp)
			}
		case models.CPIExp:
			rc.CtrCpa = data.CtrV3Instance()
			rc.CvrCpa = data.CvrV2Instance()
			fp = registry.GetFeaturesProcessing(process.NameCPIExp) // COPY NameCtcvrV3
			predict = registry.GetPredict(lightgbm.NameCPIExp)      // COPY NameCtcvr3Exp
			if req.AdSlotInfo.AdSlotId == models.TagIdOppo1 || req.AdSlotInfo.AdSlotId == models.TagIdOppo2 {
				rc.CvrV1 = data.CvrOppoInstance()
				rc.CvrCpa = data.CvrOppoInstance()
				fp = registry.GetFeaturesProcessing(process.NameCPIExpOppo) //  COPY NameCtcvrExpOppo
				predict = registry.GetPredict(lightgbm.NameOppoCpiNew)      //  COPY NameOppoExp
			}
		case models.SmoothExp:
			rc.CtrV1 = data.CtrV5Instance()
			rc.CvrV1 = data.CvrV4Instance()
			fp = registry.GetFeaturesProcessing(process.NameCtcvrV3Smooth)
			predict = registry.GetPredict(lightgbm.NameSmooth)
		default:
			if strings.HasSuffix(exp, models.CPASuffix) {
				rc.CtrCpa = data.CtrCPAInstance()
				rc.CvrCpa = data.CvrCPAInstance()
				fp = registry.GetFeaturesProcessing(process.NameCtcvrCPAGroup)
				predict = registry.GetPredict(lightgbm.NameCPA)
			} else {
				rc.CtrV1 = data.CtrV3Instance()
				rc.CvrV1 = data.CvrV2Instance()
				fp = registry.GetFeaturesProcessing(process.NameCtcvrV3)
				predict = registry.GetPredict(lightgbm.NameCtcvr3Exp)
			}
			if req.AdSlotInfo.AdSlotId == models.TagIdOppo1 || req.AdSlotInfo.AdSlotId == models.TagIdOppo2 {
				rc.CvrV1 = data.CvrOppoInstance()
				rc.CvrCpa = data.CvrOppoInstance()
				if strings.HasSuffix(exp, models.CPASuffix) {
					fp = registry.GetFeaturesProcessing(process.NameCtcvrOppo)
					predict = registry.GetPredict(lightgbm.NameOppo)
				} else {
					fp = registry.GetFeaturesProcessing(process.NameCtcvrOppoCpi)
					predict = registry.GetPredict(lightgbm.NameOppoCpi)
				}
			}
		}
		fp.MachiningFeature(rc)
		err := predict.Predict(rc)
		if err != nil {
			logger.Instance().Error("Predict err %v, requestid = %s", err, rc.RequestId)
			rc.Response.State = 1
			rc.Response.Message = fmt.Sprintf("Predict err %v", err)
			return rc.Response, err
		}
	}
	pack.Pack(rc)
	if rc.Response.State == 0 {
		dump.Dump(rc)
	}
	if apollo.ApolloRankerConfig().ReqLogEnable() {
		logger.Instance().Info("rank response: %s", json2Str(rc.Response))
	}
	return rc.Response, nil
}
