package server

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/dump/kafkadump"
	"gitlab.ydmob.com/algorithm/adranker/feature/fea"
	"gitlab.ydmob.com/algorithm/adranker/feature/process"
	"gitlab.ydmob.com/algorithm/adranker/functionlog"
	"gitlab.ydmob.com/algorithm/adranker/functionlog/promclient"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/pack/adresponse"
	"gitlab.ydmob.com/algorithm/adranker/predict/lightgbm"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
)

type RankServer struct {
	ranker.UnimplementedRankServiceServer
}

func (s *RankServer) Rank(ctx context.Context, req *ranker.AdRequestData) (*ranker.AdResponseData, error) {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		if duration > 200*time.Millisecond {
			logger.Instance().Warn("Slow request detected! RequestID: %s, Duration: %v, Request: %s",
				req.GetRequestId(),
				duration,
				json2Str(req),
			)
		}
	}()

	if req == nil {
		return nil, fmt.Errorf("invalid request")
	}
	if req.AdSlotInfo == nil || req.DeviceInfo == nil || len(req.AdMaterialInfo) == 0 {
		return nil, fmt.Errorf("invalid request")
	}
	//if models.AbTestTagId != req.AdSlotInfo.AdSlotId {
	//	return nil, fmt.Errorf("invalid tag_id")
	//}
	modifyReq(req)
	if apollo.ApolloRankerConfig().ReqLogEnable() {
		logger.Instance().Info("rank request: %s", json2Str(req))
	}

	rc := models.NewRankContext(req)
	rc.CtrV1 = data.CtrV3Instance()
	rc.CvrV1 = data.CvrV2Instance()
	rc.CtrCpa = data.CtrCPAInstance()
	rc.CvrCpa = data.CvrCPAInstance()

	rc.BiddingV1 = data.BiddingV1Instance()
	defer tools.Recover()
	funcName := "rank_server_rank"
	status := functionlog.FuncStatusOK
	var message string
	defer functionlog.DeferFunctionMonitorSDK(&funcName, &status, &message)()
	// 添加传入广告数量的统计
	promclient.CounterValueAdd("req_ad_num", float64(len(req.AdMaterialInfo)))

	if req.Version == 1 {
		// 直接根据version分开处理过程
		return RankNew(ctx, req, rc)
	} else if req.Version == 2 {
		return RankOnline(ctx, req, rc)
	}
	var (
		fe      = registry.GetFeaExtraction(fea.Name)
		fp      = registry.GetFeaturesProcessing(process.NameCtrV1)
		predict = registry.GetPredict(lightgbm.Name)
		pack    = registry.GetResultPack(adresponse.Name)
		//explore = registry.GetTrafficExplore(adresponse.Name)
		dump = registry.GetLogDump(kafkadump.Name)
	)
	if rc.Exps[models.AB20Exp7] {
		fp = registry.GetFeaturesProcessing(process.NameCtcvrV1)
		predict = registry.GetPredict(lightgbm.NameCtcvr)
	} else if rc.Exps[models.AB20Exp8] {
		fp = registry.GetFeaturesProcessing(process.NameBiddingV1)
		predict = registry.GetPredict(lightgbm.NameBidding)
	}

	fe.GetFeatures(rc)
	fp.MachiningFeature(rc)
	err := predict.Predict(rc)
	if err != nil {
		logger.Instance().Error("Predict err %v, requestid = %s", err, rc.RequestId)
		rc.Response.State = 1
		rc.Response.Message = fmt.Sprintf("Predict err %v", err)
		return rc.Response, err
	}
	//explore.Explore(rc)
	pack.Pack(rc)
	if rc.Response.State == 0 {
		dump.Dump(rc)
	}
	if apollo.ApolloRankerConfig().ReqLogEnable() {
		logger.Instance().Info("rank response: %s", json2Str(rc.Response))
	}
	return rc.Response, nil
}

func json2Str(v interface{}) string {
	b, _ := json.Marshal(v)
	return string(b)
}

func modifyReq(req *ranker.AdRequestData) {
	for _, v := range req.AdMaterialInfo {
		if v.VideoMaterialId == "" {
			v.VideoMaterialId = "0"
		}
	}
}
