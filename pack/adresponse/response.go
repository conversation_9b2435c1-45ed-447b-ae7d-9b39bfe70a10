package adresponse

import (
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog/promclient"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math"
	"sort"
)

const (
	Name = "response_pack"
)

func init() {
	registry.RegisterResultPack(Name, &ResponsePack{})
}

type ResponsePack struct {
}

func (r *ResponsePack) Init(config map[string]string) {
	return
}

func (r *ResponsePack) Pack(rc *models.RankContext) {
	rc.Response = &ranker.AdResponseData{
		State:     0,
		RequestId: rc.RequestId,
		Message:   "success",
	}
	if rc.PredictData == nil || rc.PredictData.CtrData == nil || rc.PredictData.CvrData == nil {
		rc.Response.State = 1
		rc.Response.Message = "predict error"
		return
	}
	for i, item := range rc.Request.AdMaterialInfo {
		ctr, cvr := rc.PredictData.CtrData.Data[i], rc.PredictData.CvrData.Data[i]
		if rc.PredictData.CtrCaliData != nil {
			ctr = rc.PredictData.CtrCaliData.Data[i]
		}
		if rc.PredictData.CvrCaliData != nil {
			cvr = rc.PredictData.CvrCaliData.Data[i]
		}
		var bidOri float64
		if rc.PredictData.BiddingData != nil {
			bidOri = rc.PredictData.BiddingData.Data[i]
		} else {
			bidOri = item.Cpa * ctr * cvr * 1000
		}
		bid := math.Round(bidOri)
		ecpm := data.ScInstance().Ecpm(rc.Request.AdSlotInfo.AdSlotId, item.AdId)
		if bid < 1 && bidOri > 0.000001 {
			promclient.CounterValueAdd("bid_less_than_1_"+rc.Request.AbTest, 1)
			logger.Instance().Info("bid_less_than_1, request_id = %s, bid = %f, ecpm = %f, cpa = %f, ctr = %f, cvr = %f, Mult = %f, adid = %s, abtest = %s",
				rc.RequestId, bidOri, ecpm, item.Cpa, ctr, cvr, apollo.ApolloRankerConfig().GetEcpmMultiplierLimit(), item.AdId, rc.Request.AbTest)
		}
		if bid > ecpm*apollo.ApolloRankerConfig().GetEcpmMultiplierLimit() {
			promclient.CounterValueAdd("ecpm_limit_num_"+rc.Request.AbTest, 1)
			logger.Instance().Info("ecpm_limit_num, request_id = %s, bid = %f, ecpm = %f, cpa = %f, ctr = %f, cvr = %f, Mult = %f, adid = %s, abtest = %s",
				rc.RequestId, bid, ecpm, item.Cpa, ctr, cvr, apollo.ApolloRankerConfig().GetEcpmMultiplierLimit(), item.AdId, rc.Request.AbTest)
			continue
		}
		rc.Response.ResponseItems = append(rc.Response.ResponseItems, &ranker.AdResponseItem{
			AdId:            item.AdId,
			Ctr:             ctr,
			Cvr:             cvr,
			CreativeGroupId: item.CreativeGroupId,
			Bid:             bid,
		})
	}
	sort.Slice(rc.Response.ResponseItems, func(i, j int) bool {
		return rc.Response.ResponseItems[i].Bid > rc.Response.ResponseItems[j].Bid
	})
	limit := apollo.ApolloRankerConfig().GetResponseAdNum()
	if len(rc.Response.ResponseItems) > limit {
		rc.Response.ResponseItems = rc.Response.ResponseItems[:limit]
	}
	return
}
