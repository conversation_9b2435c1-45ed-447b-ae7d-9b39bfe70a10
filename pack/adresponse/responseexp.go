package adresponse

import (
	"gitlab.ydmob.com/algorithm/adranker/apollo"
	"gitlab.ydmob.com/algorithm/adranker/data"
	"gitlab.ydmob.com/algorithm/adranker/functionlog/promclient"
	"gitlab.ydmob.com/algorithm/adranker/models"
	"gitlab.ydmob.com/algorithm/adranker/ranker"
	"gitlab.ydmob.com/algorithm/adranker/registry"
	"gitlab.ydmob.com/algorithm/adranker/tools"
	"gitlab.ydmob.com/algorithm/brief-framework/logger"
	"math"
)

const (
	NameExp = "response_pack_exp"
)

func init() {
	registry.RegisterResultPack(NameExp, &ResponsePackExp{})
}

type ResponsePackExp struct {
}

func (r *ResponsePackExp) Init(config map[string]string) {
	return
}

func (r *ResponsePackExp) Pack(rc *models.RankContext) {
	defer tools.Recover()
	rc.Response = &ranker.AdResponseData{
		State:     0,
		RequestId: rc.RequestId,
		Message:   "success",
	}
	if rc.PredictDataExp == nil || len(rc.PredictDataExp) == 0 {
		rc.Response.State = 1
		rc.Response.Message = "predict error"
		return
	}
	tmp := make(map[string][]*ranker.AdBidItem)
	for name, mis := range rc.ExpAds {
		for i, item := range mis {
			ctr, cvr := rc.PredictDataExp[name].CtrData.Data[i], rc.PredictDataExp[name].CvrData.Data[i]
			if rc.PredictDataExp[name].CtrCaliData != nil {
				ctr = rc.PredictDataExp[name].CtrCaliData.Data[i]
			}
			if rc.PredictDataExp[name].CvrCaliData != nil {
				cvr = rc.PredictDataExp[name].CvrCaliData.Data[i]
			}
			var bidOri float64
			if rc.PredictDataExp[name].BiddingData != nil {
				bidOri = rc.PredictDataExp[name].BiddingData.Data[i]
			} else {
				bidOri = item.Cpa * ctr * cvr * 1000
			}
			bid := bidOri
			ecpm := data.ScInstance().Ecpm(rc.Request.AdSlotInfo.AdSlotId, item.AdId)
			var bid2 float64
			// exp8
			if rc.PredictDataExp[name].PredictBidData != nil {
				bid2 = rc.PredictDataExp[name].PredictBidData.Data[i]
				if bid2 < 0.5 {
					promclient.CounterValueAdd("bid_less_than_1_"+rc.Request.AbTest, 1)
					logger.Instance().Info("bid_less_than_1, request_id = %s, bid = %f, ecpm = %f, cpa = %f, ctr = %f, cvr = %f, Mult = %f, adid = %s, abtest = %s",
						rc.RequestId, bid2, ecpm, item.Cpa, ctr, cvr, apollo.ApolloRankerConfig().GetEcpmMultiplierLimit(), item.AdId, name)
				}
			} else if bid < 0.5 { // exp6 7
				promclient.CounterValueAdd("bid_less_than_1_"+rc.Request.AbTest, 1)
				logger.Instance().Info("bid_less_than_1, request_id = %s, bid = %f, ecpm = %f, cpa = %f, ctr = %f, cvr = %f, Mult = %f, adid = %s, abtest = %s",
					rc.RequestId, bid, ecpm, item.Cpa, ctr, cvr, apollo.ApolloRankerConfig().GetEcpmMultiplierLimit(), item.AdId, name)
			}
			if bid > ecpm*apollo.ApolloRankerConfig().GetEcpmMultiplierLimit() {
				promclient.CounterValueAdd("ecpm_limit_num_"+rc.Request.AbTest, 1)
				logger.Instance().Info("ecpm_limit_num, request_id = %s, bid = %f, ecpm = %f, cpa = %f, ctr = %f, cvr = %f, Mult = %f, adid = %s, abtest = %s",
					rc.RequestId, bid, ecpm, item.Cpa, ctr, cvr, apollo.ApolloRankerConfig().GetEcpmMultiplierLimit(), item.AdId, rc.Request.AbTest)
				// 如果大了就取上限
				bid = ecpm * apollo.ApolloRankerConfig().GetEcpmMultiplierLimit()
			}
			key := item.AdId + "_" + item.CreativeGroupId

			bidMinEcpm := apollo.ApolloRankerConfig().GetBidMinEcpm()
			// cpa的暂时不做这个限制
			if math.Round(bid) < bidMinEcpm && item.ConversionType == 1 {
				promclient.CounterValueAdd("bid_less_than_min_ecpm", 1)
				bid = 0
			}

			tmp[key] = append(tmp[key], &ranker.AdBidItem{
				Flag: rc.ExpNames[name][i],
				Bid:  math.Round(bid),
				Ctr:  ctr,
				Cvr:  cvr,
			})
		}
	}
	zeroResponse := true
	for _, item := range rc.Request.AdMaterialInfo {
		bidItems := tmp[item.AdId+"_"+item.CreativeGroupId]
		for _, bidItem := range bidItems {
			if bidItem.Bid > 0 {
				zeroResponse = false
			}
		}
		rc.Response.ResponseItems = append(rc.Response.ResponseItems, &ranker.AdResponseItem{
			AdId:            item.AdId,
			CreativeGroupId: item.CreativeGroupId,
			BidItems:        bidItems,
		})
	}
	if zeroResponse {
		promclient.CounterValueAdd("zero_response", 1)
	}
	// 已经不需要排序
	limit := apollo.ApolloRankerConfig().GetResponseAdNum()
	if len(rc.Response.ResponseItems) > limit {
		rc.Response.ResponseItems = rc.Response.ResponseItems[:limit]
	}
	return
}
